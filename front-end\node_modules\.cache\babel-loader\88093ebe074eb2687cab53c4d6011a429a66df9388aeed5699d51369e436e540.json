{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Gestion de facture et devis\\\\front-end\\\\src\\\\components\\\\BonLivraisonForm.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Grid, Card, CardContent, TextField, Button, Typography, Autocomplete, Alert, CircularProgress, Divider, Chip } from '@mui/material';\nimport bonLivraisonService from '../services/bonLivraisonService';\nimport livreurService from '../services/livreurService';\nimport factureService from '../services/factureService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst BonLivraisonForm = ({\n  bonLivraison,\n  onSubmit,\n  onCancel\n}) => {\n  _s();\n  var _selectedFacture$clie5;\n  const [formData, setFormData] = useState({\n    livreurId: '',\n    factureId: ''\n  });\n  const [errors, setErrors] = useState({});\n  const [loading, setLoading] = useState(false);\n  const [dataLoading, setDataLoading] = useState(true);\n  const [livreurs, setLivreurs] = useState([]);\n  const [factures, setFactures] = useState([]);\n  const [selectedFacture, setSelectedFacture] = useState(null);\n  const [loadingError, setLoadingError] = useState(null);\n\n  // Charger les données nécessaires\n  useEffect(() => {\n    const loadData = async () => {\n      try {\n        setDataLoading(true);\n        setLoadingError(null);\n        console.log('🔄 Chargement des données pour le formulaire...');\n        const [livreursData, facturesData] = await Promise.all([livreurService.getLivreursDisponibles(), factureService.getFactures()]);\n        console.log('✅ Données chargées:', {\n          livreurs: (livreursData === null || livreursData === void 0 ? void 0 : livreursData.length) || 0,\n          factures: (facturesData === null || facturesData === void 0 ? void 0 : facturesData.length) || 0\n        });\n        setLivreurs(livreursData || []);\n        // Filtrer seulement les factures payées ou envoyées\n        const facturesDisponibles = (facturesData || []).filter(f => f.statut === 'PAID' || f.statut === 'SENT');\n        setFactures(facturesDisponibles);\n      } catch (error) {\n        console.error('❌ Erreur lors du chargement des données:', error);\n        setLoadingError('Erreur lors du chargement des données. Veuillez réessayer.');\n        setLivreurs([]);\n        setFactures([]);\n      } finally {\n        setDataLoading(false);\n      }\n    };\n    loadData();\n  }, []);\n\n  // Initialiser le formulaire avec les données du bon de livraison si en mode édition\n  useEffect(() => {\n    if (bonLivraison) {\n      var _bonLivraison$livreur, _bonLivraison$facture;\n      setFormData({\n        livreurId: ((_bonLivraison$livreur = bonLivraison.livreurId) === null || _bonLivraison$livreur === void 0 ? void 0 : _bonLivraison$livreur._id) || '',\n        factureId: ((_bonLivraison$facture = bonLivraison.factureId) === null || _bonLivraison$facture === void 0 ? void 0 : _bonLivraison$facture._id) || ''\n      });\n      if (bonLivraison.factureId) {\n        setSelectedFacture(bonLivraison.factureId);\n      }\n    }\n  }, [bonLivraison]);\n\n  // Gérer les changements de champs\n  const handleChange = (field, value) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n\n    // Effacer l'erreur pour ce champ\n    if (errors[field]) {\n      setErrors(prev => ({\n        ...prev,\n        [field]: ''\n      }));\n    }\n  };\n\n  // Gérer les changements de facture\n  const handleFactureChange = factureId => {\n    const facture = factures.find(f => f._id === factureId);\n    setSelectedFacture(facture);\n    handleChange('factureId', factureId);\n  };\n\n  // Formater le montant\n  const formatMontant = montant => {\n    if (!montant) return '0.00 DT';\n    return new Intl.NumberFormat('fr-TN', {\n      minimumFractionDigits: 2,\n      maximumFractionDigits: 2\n    }).format(montant) + ' DT';\n  };\n\n  // Valider et soumettre le formulaire\n  const handleSubmit = async e => {\n    e.preventDefault();\n\n    // Validation complète\n    const newErrors = {};\n    if (!formData.livreurId) {\n      newErrors.livreurId = 'Le livreur est obligatoire';\n    }\n    if (!formData.factureId) {\n      newErrors.factureId = 'La facture est obligatoire';\n    }\n    if (!selectedFacture) {\n      newErrors.factureId = 'Veuillez sélectionner une facture valide';\n    }\n    if (Object.keys(newErrors).length > 0) {\n      setErrors(newErrors);\n      return;\n    }\n    setLoading(true);\n    try {\n      var _selectedFacture$clie, _selectedFacture$clie2, _selectedFacture$clie3, _selectedFacture$clie4, _selectedFacture$lign;\n      // Préparer les données pour la soumission avec tous les champs requis\n      const submissionData = {\n        ...formData,\n        // Informations de base\n        clientId: (selectedFacture === null || selectedFacture === void 0 ? void 0 : (_selectedFacture$clie = selectedFacture.clientId) === null || _selectedFacture$clie === void 0 ? void 0 : _selectedFacture$clie._id) || (selectedFacture === null || selectedFacture === void 0 ? void 0 : selectedFacture.clientId),\n        montantTotal: (selectedFacture === null || selectedFacture === void 0 ? void 0 : selectedFacture.total) || 0,\n        dateLivraison: new Date().toISOString().split('T')[0],\n        // Date du jour par défaut\n        statut: 'EN_PREPARATION',\n        // Adresse de livraison (récupérée du client de la facture)\n        adresseLivraison: {\n          adresse: (selectedFacture === null || selectedFacture === void 0 ? void 0 : (_selectedFacture$clie2 = selectedFacture.clientId) === null || _selectedFacture$clie2 === void 0 ? void 0 : _selectedFacture$clie2.adresse) || 'Adresse non spécifiée',\n          ville: (selectedFacture === null || selectedFacture === void 0 ? void 0 : (_selectedFacture$clie3 = selectedFacture.clientId) === null || _selectedFacture$clie3 === void 0 ? void 0 : _selectedFacture$clie3.ville) || '',\n          codePostal: (selectedFacture === null || selectedFacture === void 0 ? void 0 : (_selectedFacture$clie4 = selectedFacture.clientId) === null || _selectedFacture$clie4 === void 0 ? void 0 : _selectedFacture$clie4.codePostal) || '',\n          instructions: ''\n        },\n        // Lignes de produits (récupérées de la facture)\n        lignes: (selectedFacture === null || selectedFacture === void 0 ? void 0 : (_selectedFacture$lign = selectedFacture.lignes) === null || _selectedFacture$lign === void 0 ? void 0 : _selectedFacture$lign.map((ligne, index) => ({\n          produitId: ligne.produitId || ligne._id || `temp_product_${index}`,\n          nomProduit: ligne.nomProduit || ligne.nom || ligne.description || `Produit ${index + 1}`,\n          description: ligne.description || ligne.nomProduit || ligne.nom || '',\n          quantiteLivree: ligne.quantite || 1,\n          quantiteCommandee: ligne.quantite || 1,\n          unite: ligne.unite || 'unité',\n          prixUnitaire: ligne.prixUnitaire || 0,\n          montantLigne: ligne.montantLigne || ligne.quantite * ligne.prixUnitaire || 0,\n          notes: ''\n        }))) || [],\n        // Montants\n        montantHT: (selectedFacture === null || selectedFacture === void 0 ? void 0 : selectedFacture.sousTotal) || 0,\n        montantTTC: (selectedFacture === null || selectedFacture === void 0 ? void 0 : selectedFacture.total) || 0,\n        tauxTVA: (selectedFacture === null || selectedFacture === void 0 ? void 0 : selectedFacture.tauxTVA) || 19,\n        // Notes\n        notes: `Bon de livraison généré automatiquement pour la facture ${(selectedFacture === null || selectedFacture === void 0 ? void 0 : selectedFacture.numero) || ''}`\n      };\n      console.log('📦 Données de soumission du bon de livraison:', submissionData);\n      await onSubmit(submissionData);\n    } catch (error) {\n      var _error$response, _error$response2, _error$response2$data, _error$response3, _error$response3$data;\n      console.error('Erreur lors de la soumission:', error);\n      console.error('Détails de l\\'erreur backend:', (_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.data);\n\n      // Afficher les erreurs spécifiques du backend\n      let errorMessage = 'Erreur lors de la création du bon de livraison';\n      if ((_error$response2 = error.response) !== null && _error$response2 !== void 0 && (_error$response2$data = _error$response2.data) !== null && _error$response2$data !== void 0 && _error$response2$data.errors && Array.isArray(error.response.data.errors)) {\n        errorMessage = error.response.data.errors.join(', ');\n      } else if ((_error$response3 = error.response) !== null && _error$response3 !== void 0 && (_error$response3$data = _error$response3.data) !== null && _error$response3$data !== void 0 && _error$response3$data.message) {\n        errorMessage = error.response.data.message;\n      }\n      setErrors({\n        submit: errorMessage\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Affichage du chargement initial\n  if (dataLoading) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'center',\n        alignItems: 'center',\n        minHeight: 200\n      },\n      children: [/*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 206,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body1\",\n        sx: {\n          ml: 2\n        },\n        children: \"Chargement des donn\\xE9es...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 207,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 205,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    component: \"form\",\n    onSubmit: handleSubmit,\n    sx: {\n      mt: 2\n    },\n    children: [loadingError && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"error\",\n      sx: {\n        mb: 2\n      },\n      children: loadingError\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 218,\n      columnNumber: 9\n    }, this), errors.submit && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"error\",\n      sx: {\n        mb: 2\n      },\n      children: errors.submit\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 225,\n      columnNumber: 9\n    }, this), process.env.NODE_ENV === 'development' && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"info\",\n      sx: {\n        mb: 2\n      },\n      children: [\"Donn\\xE9es disponibles: \", livreurs.length, \" livreurs, \", factures.length, \" factures\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 232,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              gutterBottom: true,\n              children: \"S\\xE9lection de la facture\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 242,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              container: true,\n              spacing: 2,\n              children: /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                children: /*#__PURE__*/_jsxDEV(Autocomplete, {\n                  options: factures,\n                  getOptionLabel: option => {\n                    var _option$clientId;\n                    return `${option.numero} - ${((_option$clientId = option.clientId) === null || _option$clientId === void 0 ? void 0 : _option$clientId.nom) || 'Client inconnu'}`;\n                  },\n                  value: factures.find(f => f._id === formData.factureId) || null,\n                  onChange: (event, newValue) => {\n                    handleFactureChange((newValue === null || newValue === void 0 ? void 0 : newValue._id) || '');\n                  },\n                  noOptionsText: factures.length === 0 ? \"Aucune facture disponible\" : \"Aucune facture trouvée\",\n                  renderInput: params => /*#__PURE__*/_jsxDEV(TextField, {\n                    ...params,\n                    label: \"Facture *\",\n                    error: !!errors.factureId,\n                    helperText: errors.factureId || (factures.length === 0 ? \"Aucune facture disponible\" : \"\")\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 256,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 247,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 246,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 245,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 241,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 240,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 239,\n        columnNumber: 9\n      }, this), selectedFacture && /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              gutterBottom: true,\n              children: \"Informations de la facture\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 275,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              container: true,\n              spacing: 2,\n              children: [/*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                md: 6,\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  fullWidth: true,\n                  label: \"Num\\xE9ro de facture\",\n                  value: selectedFacture.numero || '',\n                  InputProps: {\n                    readOnly: true\n                  },\n                  variant: \"outlined\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 280,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 279,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                md: 6,\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  fullWidth: true,\n                  label: \"Date de facture\",\n                  value: selectedFacture.dateEmission ? new Date(selectedFacture.dateEmission).toLocaleDateString('fr-FR') : '',\n                  InputProps: {\n                    readOnly: true\n                  },\n                  variant: \"outlined\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 289,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 288,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                md: 6,\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  fullWidth: true,\n                  label: \"Client\",\n                  value: ((_selectedFacture$clie5 = selectedFacture.clientId) === null || _selectedFacture$clie5 === void 0 ? void 0 : _selectedFacture$clie5.nom) || 'Client inconnu',\n                  InputProps: {\n                    readOnly: true\n                  },\n                  variant: \"outlined\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 299,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 298,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                md: 6,\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  fullWidth: true,\n                  label: \"Montant total\",\n                  value: formatMontant(selectedFacture.total),\n                  InputProps: {\n                    readOnly: true\n                  },\n                  variant: \"outlined\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 308,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 307,\n                columnNumber: 19\n              }, this), selectedFacture.lignes && selectedFacture.lignes.length > 0 && /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle2\",\n                  gutterBottom: true,\n                  children: \"Produits \\xE0 livrer:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 318,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    flexWrap: 'wrap',\n                    gap: 1\n                  },\n                  children: selectedFacture.lignes.map((ligne, index) => /*#__PURE__*/_jsxDEV(Chip, {\n                    label: `${ligne.description || 'Produit'} (Qté: ${ligne.quantite})`,\n                    variant: \"outlined\",\n                    size: \"small\"\n                  }, index, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 323,\n                    columnNumber: 27\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 321,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 317,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 278,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 274,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 273,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 272,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              gutterBottom: true,\n              children: \"Livreur\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 343,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              container: true,\n              spacing: 2,\n              children: /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                children: /*#__PURE__*/_jsxDEV(Autocomplete, {\n                  options: livreurs,\n                  getOptionLabel: option => `${option.prenom} ${option.nom}` || '',\n                  value: livreurs.find(l => l._id === formData.livreurId) || null,\n                  onChange: (event, newValue) => {\n                    handleChange('livreurId', (newValue === null || newValue === void 0 ? void 0 : newValue._id) || '');\n                  },\n                  noOptionsText: livreurs.length === 0 ? \"Aucun livreur disponible\" : \"Aucun livreur trouvé\",\n                  renderInput: params => /*#__PURE__*/_jsxDEV(TextField, {\n                    ...params,\n                    label: \"Livreur *\",\n                    error: !!errors.livreurId,\n                    helperText: errors.livreurId || (livreurs.length === 0 ? \"Aucun livreur disponible\" : \"\")\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 357,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 348,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 347,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 346,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 342,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 341,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 340,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'flex-end',\n            gap: 2,\n            mt: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            onClick: onCancel,\n            disabled: loading,\n            children: \"Annuler\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 374,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            type: \"submit\",\n            variant: \"contained\",\n            disabled: loading,\n            children: loading ? 'Enregistrement...' : bonLivraison ? 'Modifier' : 'Créer'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 381,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 373,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 372,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 237,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 215,\n    columnNumber: 5\n  }, this);\n};\n_s(BonLivraisonForm, \"r7zo9u6Wl/PQDx20SpFWn0ovEdc=\");\n_c = BonLivraisonForm;\nexport default BonLivraisonForm;\nvar _c;\n$RefreshReg$(_c, \"BonLivraisonForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "TextField", "<PERSON><PERSON>", "Typography", "Autocomplete", "<PERSON><PERSON>", "CircularProgress", "Divider", "Chip", "bonLivraisonService", "livreurService", "factureService", "jsxDEV", "_jsxDEV", "BonLivraisonForm", "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "onSubmit", "onCancel", "_s", "_selectedFacture$clie5", "formData", "setFormData", "livreurId", "factureId", "errors", "setErrors", "loading", "setLoading", "dataLoading", "setDataLoading", "livreurs", "setLivreurs", "factures", "setFactures", "selectedFacture", "setSelectedFacture", "loadingError", "setLoadingError", "loadData", "console", "log", "livreursData", "facturesData", "Promise", "all", "getLivreursDisponibles", "getFactures", "length", "facturesDisponibles", "filter", "f", "statut", "error", "_bonLivraison$livreur", "_bonLivraison$facture", "_id", "handleChange", "field", "value", "prev", "handleFactureChange", "facture", "find", "formatMontant", "montant", "Intl", "NumberFormat", "minimumFractionDigits", "maximumFractionDigits", "format", "handleSubmit", "e", "preventDefault", "newErrors", "Object", "keys", "_selectedFacture$clie", "_selectedFacture$clie2", "_selectedFacture$clie3", "_selectedFacture$clie4", "_selectedFacture$lign", "submissionData", "clientId", "montantTotal", "total", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Date", "toISOString", "split", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "adresse", "ville", "codePostal", "instructions", "lignes", "map", "ligne", "index", "produitId", "nomProduit", "nom", "description", "quantite<PERSON><PERSON><PERSON>", "quantite", "quantite<PERSON><PERSON><PERSON>ee", "unite", "prix<PERSON>ni<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "notes", "montantHT", "sousTotal", "montantTTC", "tauxTVA", "numero", "_error$response", "_error$response2", "_error$response2$data", "_error$response3", "_error$response3$data", "response", "data", "errorMessage", "Array", "isArray", "join", "message", "submit", "sx", "display", "justifyContent", "alignItems", "minHeight", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "ml", "component", "mt", "severity", "mb", "process", "env", "NODE_ENV", "container", "spacing", "item", "xs", "gutterBottom", "options", "getOptionLabel", "option", "_option$clientId", "onChange", "event", "newValue", "noOptionsText", "renderInput", "params", "label", "helperText", "md", "fullWidth", "InputProps", "readOnly", "dateEmission", "toLocaleDateString", "flexWrap", "gap", "size", "prenom", "l", "onClick", "disabled", "type", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Gestion de facture et devis/front-end/src/components/BonLivraisonForm.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Grid,\n  Card,\n  CardContent,\n  TextField,\n  Button,\n  Typography,\n  Autocomplete,\n  Alert,\n  CircularProgress,\n  Divider,\n  Chip\n} from '@mui/material';\n\nimport bonLivraisonService from '../services/bonLivraisonService';\nimport livreurService from '../services/livreurService';\nimport factureService from '../services/factureService';\n\nconst BonLivraisonForm = ({ bonLivraison, onSubmit, onCancel }) => {\n  const [formData, setFormData] = useState({\n    livreurId: '',\n    factureId: ''\n  });\n\n  const [errors, setErrors] = useState({});\n  const [loading, setLoading] = useState(false);\n  const [dataLoading, setDataLoading] = useState(true);\n  const [livreurs, setLivreurs] = useState([]);\n  const [factures, setFactures] = useState([]);\n  const [selectedFacture, setSelectedFacture] = useState(null);\n  const [loadingError, setLoadingError] = useState(null);\n\n  // Charger les données nécessaires\n  useEffect(() => {\n    const loadData = async () => {\n      try {\n        setDataLoading(true);\n        setLoadingError(null);\n        console.log('🔄 Chargement des données pour le formulaire...');\n\n        const [livreursData, facturesData] = await Promise.all([\n          livreurService.getLivreursDisponibles(),\n          factureService.getFactures()\n        ]);\n\n        console.log('✅ Données chargées:', {\n          livreurs: livreursData?.length || 0,\n          factures: facturesData?.length || 0\n        });\n\n        setLivreurs(livreursData || []);\n        // Filtrer seulement les factures payées ou envoyées\n        const facturesDisponibles = (facturesData || []).filter(f =>\n          f.statut === 'PAID' || f.statut === 'SENT'\n        );\n        setFactures(facturesDisponibles);\n      } catch (error) {\n        console.error('❌ Erreur lors du chargement des données:', error);\n        setLoadingError('Erreur lors du chargement des données. Veuillez réessayer.');\n        setLivreurs([]);\n        setFactures([]);\n      } finally {\n        setDataLoading(false);\n      }\n    };\n\n    loadData();\n  }, []);\n\n  // Initialiser le formulaire avec les données du bon de livraison si en mode édition\n  useEffect(() => {\n    if (bonLivraison) {\n      setFormData({\n        livreurId: bonLivraison.livreurId?._id || '',\n        factureId: bonLivraison.factureId?._id || ''\n      });\n\n      if (bonLivraison.factureId) {\n        setSelectedFacture(bonLivraison.factureId);\n      }\n    }\n  }, [bonLivraison]);\n\n  // Gérer les changements de champs\n  const handleChange = (field, value) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n\n    // Effacer l'erreur pour ce champ\n    if (errors[field]) {\n      setErrors(prev => ({\n        ...prev,\n        [field]: ''\n      }));\n    }\n  };\n\n  // Gérer les changements de facture\n  const handleFactureChange = (factureId) => {\n    const facture = factures.find(f => f._id === factureId);\n    setSelectedFacture(facture);\n    handleChange('factureId', factureId);\n  };\n\n  // Formater le montant\n  const formatMontant = (montant) => {\n    if (!montant) return '0.00 DT';\n    return new Intl.NumberFormat('fr-TN', {\n      minimumFractionDigits: 2,\n      maximumFractionDigits: 2\n    }).format(montant) + ' DT';\n  };\n\n  // Valider et soumettre le formulaire\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n\n    // Validation complète\n    const newErrors = {};\n    if (!formData.livreurId) {\n      newErrors.livreurId = 'Le livreur est obligatoire';\n    }\n    if (!formData.factureId) {\n      newErrors.factureId = 'La facture est obligatoire';\n    }\n    if (!selectedFacture) {\n      newErrors.factureId = 'Veuillez sélectionner une facture valide';\n    }\n\n    if (Object.keys(newErrors).length > 0) {\n      setErrors(newErrors);\n      return;\n    }\n\n    setLoading(true);\n    try {\n      // Préparer les données pour la soumission avec tous les champs requis\n      const submissionData = {\n        ...formData,\n        // Informations de base\n        clientId: selectedFacture?.clientId?._id || selectedFacture?.clientId,\n        montantTotal: selectedFacture?.total || 0,\n        dateLivraison: new Date().toISOString().split('T')[0], // Date du jour par défaut\n        statut: 'EN_PREPARATION',\n\n        // Adresse de livraison (récupérée du client de la facture)\n        adresseLivraison: {\n          adresse: selectedFacture?.clientId?.adresse || 'Adresse non spécifiée',\n          ville: selectedFacture?.clientId?.ville || '',\n          codePostal: selectedFacture?.clientId?.codePostal || '',\n          instructions: ''\n        },\n\n        // Lignes de produits (récupérées de la facture)\n        lignes: selectedFacture?.lignes?.map((ligne, index) => ({\n          produitId: ligne.produitId || ligne._id || `temp_product_${index}`,\n          nomProduit: ligne.nomProduit || ligne.nom || ligne.description || `Produit ${index + 1}`,\n          description: ligne.description || ligne.nomProduit || ligne.nom || '',\n          quantiteLivree: ligne.quantite || 1,\n          quantiteCommandee: ligne.quantite || 1,\n          unite: ligne.unite || 'unité',\n          prixUnitaire: ligne.prixUnitaire || 0,\n          montantLigne: ligne.montantLigne || (ligne.quantite * ligne.prixUnitaire) || 0,\n          notes: ''\n        })) || [],\n\n        // Montants\n        montantHT: selectedFacture?.sousTotal || 0,\n        montantTTC: selectedFacture?.total || 0,\n        tauxTVA: selectedFacture?.tauxTVA || 19,\n\n        // Notes\n        notes: `Bon de livraison généré automatiquement pour la facture ${selectedFacture?.numero || ''}`\n      };\n\n      console.log('📦 Données de soumission du bon de livraison:', submissionData);\n      await onSubmit(submissionData);\n    } catch (error) {\n      console.error('Erreur lors de la soumission:', error);\n      console.error('Détails de l\\'erreur backend:', error.response?.data);\n\n      // Afficher les erreurs spécifiques du backend\n      let errorMessage = 'Erreur lors de la création du bon de livraison';\n      if (error.response?.data?.errors && Array.isArray(error.response.data.errors)) {\n        errorMessage = error.response.data.errors.join(', ');\n      } else if (error.response?.data?.message) {\n        errorMessage = error.response.data.message;\n      }\n\n      setErrors({\n        submit: errorMessage\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Affichage du chargement initial\n  if (dataLoading) {\n    return (\n      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 200 }}>\n        <CircularProgress />\n        <Typography variant=\"body1\" sx={{ ml: 2 }}>\n          Chargement des données...\n        </Typography>\n      </Box>\n    );\n  }\n\n  return (\n    <Box component=\"form\" onSubmit={handleSubmit} sx={{ mt: 2 }}>\n      {/* Affichage des erreurs de chargement */}\n      {loadingError && (\n        <Alert severity=\"error\" sx={{ mb: 2 }}>\n          {loadingError}\n        </Alert>\n      )}\n\n      {/* Affichage des erreurs de soumission */}\n      {errors.submit && (\n        <Alert severity=\"error\" sx={{ mb: 2 }}>\n          {errors.submit}\n        </Alert>\n      )}\n\n      {/* Affichage des informations de débogage */}\n      {process.env.NODE_ENV === 'development' && (\n        <Alert severity=\"info\" sx={{ mb: 2 }}>\n          Données disponibles: {livreurs.length} livreurs, {factures.length} factures\n        </Alert>\n      )}\n\n      <Grid container spacing={3}>\n        {/* Sélection de la facture */}\n        <Grid item xs={12}>\n          <Card>\n            <CardContent>\n              <Typography variant=\"h6\" gutterBottom>\n                Sélection de la facture\n              </Typography>\n              <Grid container spacing={2}>\n                <Grid item xs={12}>\n                  <Autocomplete\n                    options={factures}\n                    getOptionLabel={(option) => `${option.numero} - ${option.clientId?.nom || 'Client inconnu'}`}\n                    value={factures.find(f => f._id === formData.factureId) || null}\n                    onChange={(event, newValue) => {\n                      handleFactureChange(newValue?._id || '');\n                    }}\n                    noOptionsText={factures.length === 0 ? \"Aucune facture disponible\" : \"Aucune facture trouvée\"}\n                    renderInput={(params) => (\n                      <TextField\n                        {...params}\n                        label=\"Facture *\"\n                        error={!!errors.factureId}\n                        helperText={errors.factureId || (factures.length === 0 ? \"Aucune facture disponible\" : \"\")}\n                      />\n                    )}\n                  />\n                </Grid>\n              </Grid>\n            </CardContent>\n          </Card>\n        </Grid>\n\n        {/* Informations de la facture sélectionnée */}\n        {selectedFacture && (\n          <Grid item xs={12}>\n            <Card>\n              <CardContent>\n                <Typography variant=\"h6\" gutterBottom>\n                  Informations de la facture\n                </Typography>\n                <Grid container spacing={2}>\n                  <Grid item xs={12} md={6}>\n                    <TextField\n                      fullWidth\n                      label=\"Numéro de facture\"\n                      value={selectedFacture.numero || ''}\n                      InputProps={{ readOnly: true }}\n                      variant=\"outlined\"\n                    />\n                  </Grid>\n                  <Grid item xs={12} md={6}>\n                    <TextField\n                      fullWidth\n                      label=\"Date de facture\"\n                      value={selectedFacture.dateEmission ?\n                        new Date(selectedFacture.dateEmission).toLocaleDateString('fr-FR') : ''}\n                      InputProps={{ readOnly: true }}\n                      variant=\"outlined\"\n                    />\n                  </Grid>\n                  <Grid item xs={12} md={6}>\n                    <TextField\n                      fullWidth\n                      label=\"Client\"\n                      value={selectedFacture.clientId?.nom || 'Client inconnu'}\n                      InputProps={{ readOnly: true }}\n                      variant=\"outlined\"\n                    />\n                  </Grid>\n                  <Grid item xs={12} md={6}>\n                    <TextField\n                      fullWidth\n                      label=\"Montant total\"\n                      value={formatMontant(selectedFacture.total)}\n                      InputProps={{ readOnly: true }}\n                      variant=\"outlined\"\n                    />\n                  </Grid>\n                  {selectedFacture.lignes && selectedFacture.lignes.length > 0 && (\n                    <Grid item xs={12}>\n                      <Typography variant=\"subtitle2\" gutterBottom>\n                        Produits à livrer:\n                      </Typography>\n                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>\n                        {selectedFacture.lignes.map((ligne, index) => (\n                          <Chip\n                            key={index}\n                            label={`${ligne.description || 'Produit'} (Qté: ${ligne.quantite})`}\n                            variant=\"outlined\"\n                            size=\"small\"\n                          />\n                        ))}\n                      </Box>\n                    </Grid>\n                  )}\n                </Grid>\n              </CardContent>\n            </Card>\n          </Grid>\n        )}\n\n        {/* Sélection du livreur */}\n        <Grid item xs={12}>\n          <Card>\n            <CardContent>\n              <Typography variant=\"h6\" gutterBottom>\n                Livreur\n              </Typography>\n              <Grid container spacing={2}>\n                <Grid item xs={12}>\n                  <Autocomplete\n                    options={livreurs}\n                    getOptionLabel={(option) => `${option.prenom} ${option.nom}` || ''}\n                    value={livreurs.find(l => l._id === formData.livreurId) || null}\n                    onChange={(event, newValue) => {\n                      handleChange('livreurId', newValue?._id || '');\n                    }}\n                    noOptionsText={livreurs.length === 0 ? \"Aucun livreur disponible\" : \"Aucun livreur trouvé\"}\n                    renderInput={(params) => (\n                      <TextField\n                        {...params}\n                        label=\"Livreur *\"\n                        error={!!errors.livreurId}\n                        helperText={errors.livreurId || (livreurs.length === 0 ? \"Aucun livreur disponible\" : \"\")}\n                      />\n                    )}\n                  />\n                </Grid>\n              </Grid>\n            </CardContent>\n          </Card>\n        </Grid>\n\n        {/* Boutons d'action */}\n        <Grid item xs={12}>\n          <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2, mt: 2 }}>\n            <Button\n              variant=\"outlined\"\n              onClick={onCancel}\n              disabled={loading}\n            >\n              Annuler\n            </Button>\n            <Button\n              type=\"submit\"\n              variant=\"contained\"\n              disabled={loading}\n            >\n              {loading ? 'Enregistrement...' : (bonLivraison ? 'Modifier' : 'Créer')}\n            </Button>\n          </Box>\n        </Grid>\n      </Grid>\n    </Box>\n  );\n};\n\nexport default BonLivraisonForm;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,SAAS,EACTC,MAAM,EACNC,UAAU,EACVC,YAAY,EACZC,KAAK,EACLC,gBAAgB,EAChBC,OAAO,EACPC,IAAI,QACC,eAAe;AAEtB,OAAOC,mBAAmB,MAAM,iCAAiC;AACjE,OAAOC,cAAc,MAAM,4BAA4B;AACvD,OAAOC,cAAc,MAAM,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExD,MAAMC,gBAAgB,GAAGA,CAAC;EAAEC,YAAY;EAAEC,QAAQ;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,sBAAA;EACjE,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG1B,QAAQ,CAAC;IACvC2B,SAAS,EAAE,EAAE;IACbC,SAAS,EAAE;EACb,CAAC,CAAC;EAEF,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAG9B,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxC,MAAM,CAAC+B,OAAO,EAAEC,UAAU,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACiC,WAAW,EAAEC,cAAc,CAAC,GAAGlC,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACmC,QAAQ,EAAEC,WAAW,CAAC,GAAGpC,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACqC,QAAQ,EAAEC,WAAW,CAAC,GAAGtC,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACuC,eAAe,EAAEC,kBAAkB,CAAC,GAAGxC,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACyC,YAAY,EAAEC,eAAe,CAAC,GAAG1C,QAAQ,CAAC,IAAI,CAAC;;EAEtD;EACAC,SAAS,CAAC,MAAM;IACd,MAAM0C,QAAQ,GAAG,MAAAA,CAAA,KAAY;MAC3B,IAAI;QACFT,cAAc,CAAC,IAAI,CAAC;QACpBQ,eAAe,CAAC,IAAI,CAAC;QACrBE,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;QAE9D,MAAM,CAACC,YAAY,EAAEC,YAAY,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CACrDlC,cAAc,CAACmC,sBAAsB,CAAC,CAAC,EACvClC,cAAc,CAACmC,WAAW,CAAC,CAAC,CAC7B,CAAC;QAEFP,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE;UACjCV,QAAQ,EAAE,CAAAW,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEM,MAAM,KAAI,CAAC;UACnCf,QAAQ,EAAE,CAAAU,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEK,MAAM,KAAI;QACpC,CAAC,CAAC;QAEFhB,WAAW,CAACU,YAAY,IAAI,EAAE,CAAC;QAC/B;QACA,MAAMO,mBAAmB,GAAG,CAACN,YAAY,IAAI,EAAE,EAAEO,MAAM,CAACC,CAAC,IACvDA,CAAC,CAACC,MAAM,KAAK,MAAM,IAAID,CAAC,CAACC,MAAM,KAAK,MACtC,CAAC;QACDlB,WAAW,CAACe,mBAAmB,CAAC;MAClC,CAAC,CAAC,OAAOI,KAAK,EAAE;QACdb,OAAO,CAACa,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;QAChEf,eAAe,CAAC,4DAA4D,CAAC;QAC7EN,WAAW,CAAC,EAAE,CAAC;QACfE,WAAW,CAAC,EAAE,CAAC;MACjB,CAAC,SAAS;QACRJ,cAAc,CAAC,KAAK,CAAC;MACvB;IACF,CAAC;IAEDS,QAAQ,CAAC,CAAC;EACZ,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA1C,SAAS,CAAC,MAAM;IACd,IAAImB,YAAY,EAAE;MAAA,IAAAsC,qBAAA,EAAAC,qBAAA;MAChBjC,WAAW,CAAC;QACVC,SAAS,EAAE,EAAA+B,qBAAA,GAAAtC,YAAY,CAACO,SAAS,cAAA+B,qBAAA,uBAAtBA,qBAAA,CAAwBE,GAAG,KAAI,EAAE;QAC5ChC,SAAS,EAAE,EAAA+B,qBAAA,GAAAvC,YAAY,CAACQ,SAAS,cAAA+B,qBAAA,uBAAtBA,qBAAA,CAAwBC,GAAG,KAAI;MAC5C,CAAC,CAAC;MAEF,IAAIxC,YAAY,CAACQ,SAAS,EAAE;QAC1BY,kBAAkB,CAACpB,YAAY,CAACQ,SAAS,CAAC;MAC5C;IACF;EACF,CAAC,EAAE,CAACR,YAAY,CAAC,CAAC;;EAElB;EACA,MAAMyC,YAAY,GAAGA,CAACC,KAAK,EAAEC,KAAK,KAAK;IACrCrC,WAAW,CAACsC,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACF,KAAK,GAAGC;IACX,CAAC,CAAC,CAAC;;IAEH;IACA,IAAIlC,MAAM,CAACiC,KAAK,CAAC,EAAE;MACjBhC,SAAS,CAACkC,IAAI,KAAK;QACjB,GAAGA,IAAI;QACP,CAACF,KAAK,GAAG;MACX,CAAC,CAAC,CAAC;IACL;EACF,CAAC;;EAED;EACA,MAAMG,mBAAmB,GAAIrC,SAAS,IAAK;IACzC,MAAMsC,OAAO,GAAG7B,QAAQ,CAAC8B,IAAI,CAACZ,CAAC,IAAIA,CAAC,CAACK,GAAG,KAAKhC,SAAS,CAAC;IACvDY,kBAAkB,CAAC0B,OAAO,CAAC;IAC3BL,YAAY,CAAC,WAAW,EAAEjC,SAAS,CAAC;EACtC,CAAC;;EAED;EACA,MAAMwC,aAAa,GAAIC,OAAO,IAAK;IACjC,IAAI,CAACA,OAAO,EAAE,OAAO,SAAS;IAC9B,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,qBAAqB,EAAE,CAAC;MACxBC,qBAAqB,EAAE;IACzB,CAAC,CAAC,CAACC,MAAM,CAACL,OAAO,CAAC,GAAG,KAAK;EAC5B,CAAC;;EAED;EACA,MAAMM,YAAY,GAAG,MAAOC,CAAC,IAAK;IAChCA,CAAC,CAACC,cAAc,CAAC,CAAC;;IAElB;IACA,MAAMC,SAAS,GAAG,CAAC,CAAC;IACpB,IAAI,CAACrD,QAAQ,CAACE,SAAS,EAAE;MACvBmD,SAAS,CAACnD,SAAS,GAAG,4BAA4B;IACpD;IACA,IAAI,CAACF,QAAQ,CAACG,SAAS,EAAE;MACvBkD,SAAS,CAAClD,SAAS,GAAG,4BAA4B;IACpD;IACA,IAAI,CAACW,eAAe,EAAE;MACpBuC,SAAS,CAAClD,SAAS,GAAG,0CAA0C;IAClE;IAEA,IAAImD,MAAM,CAACC,IAAI,CAACF,SAAS,CAAC,CAAC1B,MAAM,GAAG,CAAC,EAAE;MACrCtB,SAAS,CAACgD,SAAS,CAAC;MACpB;IACF;IAEA9C,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MAAA,IAAAiD,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,qBAAA;MACF;MACA,MAAMC,cAAc,GAAG;QACrB,GAAG7D,QAAQ;QACX;QACA8D,QAAQ,EAAE,CAAAhD,eAAe,aAAfA,eAAe,wBAAA0C,qBAAA,GAAf1C,eAAe,CAAEgD,QAAQ,cAAAN,qBAAA,uBAAzBA,qBAAA,CAA2BrB,GAAG,MAAIrB,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEgD,QAAQ;QACrEC,YAAY,EAAE,CAAAjD,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEkD,KAAK,KAAI,CAAC;QACzCC,aAAa,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAAE;QACvDrC,MAAM,EAAE,gBAAgB;QAExB;QACAsC,gBAAgB,EAAE;UAChBC,OAAO,EAAE,CAAAxD,eAAe,aAAfA,eAAe,wBAAA2C,sBAAA,GAAf3C,eAAe,CAAEgD,QAAQ,cAAAL,sBAAA,uBAAzBA,sBAAA,CAA2Ba,OAAO,KAAI,uBAAuB;UACtEC,KAAK,EAAE,CAAAzD,eAAe,aAAfA,eAAe,wBAAA4C,sBAAA,GAAf5C,eAAe,CAAEgD,QAAQ,cAAAJ,sBAAA,uBAAzBA,sBAAA,CAA2Ba,KAAK,KAAI,EAAE;UAC7CC,UAAU,EAAE,CAAA1D,eAAe,aAAfA,eAAe,wBAAA6C,sBAAA,GAAf7C,eAAe,CAAEgD,QAAQ,cAAAH,sBAAA,uBAAzBA,sBAAA,CAA2Ba,UAAU,KAAI,EAAE;UACvDC,YAAY,EAAE;QAChB,CAAC;QAED;QACAC,MAAM,EAAE,CAAA5D,eAAe,aAAfA,eAAe,wBAAA8C,qBAAA,GAAf9C,eAAe,CAAE4D,MAAM,cAAAd,qBAAA,uBAAvBA,qBAAA,CAAyBe,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,MAAM;UACtDC,SAAS,EAAEF,KAAK,CAACE,SAAS,IAAIF,KAAK,CAACzC,GAAG,IAAI,gBAAgB0C,KAAK,EAAE;UAClEE,UAAU,EAAEH,KAAK,CAACG,UAAU,IAAIH,KAAK,CAACI,GAAG,IAAIJ,KAAK,CAACK,WAAW,IAAI,WAAWJ,KAAK,GAAG,CAAC,EAAE;UACxFI,WAAW,EAAEL,KAAK,CAACK,WAAW,IAAIL,KAAK,CAACG,UAAU,IAAIH,KAAK,CAACI,GAAG,IAAI,EAAE;UACrEE,cAAc,EAAEN,KAAK,CAACO,QAAQ,IAAI,CAAC;UACnCC,iBAAiB,EAAER,KAAK,CAACO,QAAQ,IAAI,CAAC;UACtCE,KAAK,EAAET,KAAK,CAACS,KAAK,IAAI,OAAO;UAC7BC,YAAY,EAAEV,KAAK,CAACU,YAAY,IAAI,CAAC;UACrCC,YAAY,EAAEX,KAAK,CAACW,YAAY,IAAKX,KAAK,CAACO,QAAQ,GAAGP,KAAK,CAACU,YAAa,IAAI,CAAC;UAC9EE,KAAK,EAAE;QACT,CAAC,CAAC,CAAC,KAAI,EAAE;QAET;QACAC,SAAS,EAAE,CAAA3E,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE4E,SAAS,KAAI,CAAC;QAC1CC,UAAU,EAAE,CAAA7E,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEkD,KAAK,KAAI,CAAC;QACvC4B,OAAO,EAAE,CAAA9E,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE8E,OAAO,KAAI,EAAE;QAEvC;QACAJ,KAAK,EAAE,2DAA2D,CAAA1E,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE+E,MAAM,KAAI,EAAE;MACjG,CAAC;MAED1E,OAAO,CAACC,GAAG,CAAC,+CAA+C,EAAEyC,cAAc,CAAC;MAC5E,MAAMjE,QAAQ,CAACiE,cAAc,CAAC;IAChC,CAAC,CAAC,OAAO7B,KAAK,EAAE;MAAA,IAAA8D,eAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA;MACd/E,OAAO,CAACa,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrDb,OAAO,CAACa,KAAK,CAAC,+BAA+B,GAAA8D,eAAA,GAAE9D,KAAK,CAACmE,QAAQ,cAAAL,eAAA,uBAAdA,eAAA,CAAgBM,IAAI,CAAC;;MAEpE;MACA,IAAIC,YAAY,GAAG,gDAAgD;MACnE,IAAI,CAAAN,gBAAA,GAAA/D,KAAK,CAACmE,QAAQ,cAAAJ,gBAAA,gBAAAC,qBAAA,GAAdD,gBAAA,CAAgBK,IAAI,cAAAJ,qBAAA,eAApBA,qBAAA,CAAsB5F,MAAM,IAAIkG,KAAK,CAACC,OAAO,CAACvE,KAAK,CAACmE,QAAQ,CAACC,IAAI,CAAChG,MAAM,CAAC,EAAE;QAC7EiG,YAAY,GAAGrE,KAAK,CAACmE,QAAQ,CAACC,IAAI,CAAChG,MAAM,CAACoG,IAAI,CAAC,IAAI,CAAC;MACtD,CAAC,MAAM,KAAAP,gBAAA,GAAIjE,KAAK,CAACmE,QAAQ,cAAAF,gBAAA,gBAAAC,qBAAA,GAAdD,gBAAA,CAAgBG,IAAI,cAAAF,qBAAA,eAApBA,qBAAA,CAAsBO,OAAO,EAAE;QACxCJ,YAAY,GAAGrE,KAAK,CAACmE,QAAQ,CAACC,IAAI,CAACK,OAAO;MAC5C;MAEApG,SAAS,CAAC;QACRqG,MAAM,EAAEL;MACV,CAAC,CAAC;IACJ,CAAC,SAAS;MACR9F,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,IAAIC,WAAW,EAAE;IACf,oBACEf,OAAA,CAAChB,GAAG;MAACkI,EAAE,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,QAAQ;QAAEC,UAAU,EAAE,QAAQ;QAAEC,SAAS,EAAE;MAAI,CAAE;MAAAC,QAAA,gBAC3FvH,OAAA,CAACP,gBAAgB;QAAA+H,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACpB3H,OAAA,CAACV,UAAU;QAACsI,OAAO,EAAC,OAAO;QAACV,EAAE,EAAE;UAAEW,EAAE,EAAE;QAAE,CAAE;QAAAN,QAAA,EAAC;MAE3C;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAEV;EAEA,oBACE3H,OAAA,CAAChB,GAAG;IAAC8I,SAAS,EAAC,MAAM;IAAC3H,QAAQ,EAAEsD,YAAa;IAACyD,EAAE,EAAE;MAAEa,EAAE,EAAE;IAAE,CAAE;IAAAR,QAAA,GAEzDhG,YAAY,iBACXvB,OAAA,CAACR,KAAK;MAACwI,QAAQ,EAAC,OAAO;MAACd,EAAE,EAAE;QAAEe,EAAE,EAAE;MAAE,CAAE;MAAAV,QAAA,EACnChG;IAAY;MAAAiG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CACR,EAGAhH,MAAM,CAACsG,MAAM,iBACZjH,OAAA,CAACR,KAAK;MAACwI,QAAQ,EAAC,OAAO;MAACd,EAAE,EAAE;QAAEe,EAAE,EAAE;MAAE,CAAE;MAAAV,QAAA,EACnC5G,MAAM,CAACsG;IAAM;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT,CACR,EAGAO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,iBACrCpI,OAAA,CAACR,KAAK;MAACwI,QAAQ,EAAC,MAAM;MAACd,EAAE,EAAE;QAAEe,EAAE,EAAE;MAAE,CAAE;MAAAV,QAAA,GAAC,0BACf,EAACtG,QAAQ,CAACiB,MAAM,EAAC,aAAW,EAACf,QAAQ,CAACe,MAAM,EAAC,WACpE;IAAA;MAAAsF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CACR,eAED3H,OAAA,CAACf,IAAI;MAACoJ,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAf,QAAA,gBAEzBvH,OAAA,CAACf,IAAI;QAACsJ,IAAI;QAACC,EAAE,EAAE,EAAG;QAAAjB,QAAA,eAChBvH,OAAA,CAACd,IAAI;UAAAqI,QAAA,eACHvH,OAAA,CAACb,WAAW;YAAAoI,QAAA,gBACVvH,OAAA,CAACV,UAAU;cAACsI,OAAO,EAAC,IAAI;cAACa,YAAY;cAAAlB,QAAA,EAAC;YAEtC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb3H,OAAA,CAACf,IAAI;cAACoJ,SAAS;cAACC,OAAO,EAAE,CAAE;cAAAf,QAAA,eACzBvH,OAAA,CAACf,IAAI;gBAACsJ,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAAAjB,QAAA,eAChBvH,OAAA,CAACT,YAAY;kBACXmJ,OAAO,EAAEvH,QAAS;kBAClBwH,cAAc,EAAGC,MAAM;oBAAA,IAAAC,gBAAA;oBAAA,OAAK,GAAGD,MAAM,CAACxC,MAAM,MAAM,EAAAyC,gBAAA,GAAAD,MAAM,CAACvE,QAAQ,cAAAwE,gBAAA,uBAAfA,gBAAA,CAAiBtD,GAAG,KAAI,gBAAgB,EAAE;kBAAA,CAAC;kBAC7F1C,KAAK,EAAE1B,QAAQ,CAAC8B,IAAI,CAACZ,CAAC,IAAIA,CAAC,CAACK,GAAG,KAAKnC,QAAQ,CAACG,SAAS,CAAC,IAAI,IAAK;kBAChEoI,QAAQ,EAAEA,CAACC,KAAK,EAAEC,QAAQ,KAAK;oBAC7BjG,mBAAmB,CAAC,CAAAiG,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEtG,GAAG,KAAI,EAAE,CAAC;kBAC1C,CAAE;kBACFuG,aAAa,EAAE9H,QAAQ,CAACe,MAAM,KAAK,CAAC,GAAG,2BAA2B,GAAG,wBAAyB;kBAC9FgH,WAAW,EAAGC,MAAM,iBAClBnJ,OAAA,CAACZ,SAAS;oBAAA,GACJ+J,MAAM;oBACVC,KAAK,EAAC,WAAW;oBACjB7G,KAAK,EAAE,CAAC,CAAC5B,MAAM,CAACD,SAAU;oBAC1B2I,UAAU,EAAE1I,MAAM,CAACD,SAAS,KAAKS,QAAQ,CAACe,MAAM,KAAK,CAAC,GAAG,2BAA2B,GAAG,EAAE;kBAAE;oBAAAsF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5F;gBACD;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGNtG,eAAe,iBACdrB,OAAA,CAACf,IAAI;QAACsJ,IAAI;QAACC,EAAE,EAAE,EAAG;QAAAjB,QAAA,eAChBvH,OAAA,CAACd,IAAI;UAAAqI,QAAA,eACHvH,OAAA,CAACb,WAAW;YAAAoI,QAAA,gBACVvH,OAAA,CAACV,UAAU;cAACsI,OAAO,EAAC,IAAI;cAACa,YAAY;cAAAlB,QAAA,EAAC;YAEtC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb3H,OAAA,CAACf,IAAI;cAACoJ,SAAS;cAACC,OAAO,EAAE,CAAE;cAAAf,QAAA,gBACzBvH,OAAA,CAACf,IAAI;gBAACsJ,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACc,EAAE,EAAE,CAAE;gBAAA/B,QAAA,eACvBvH,OAAA,CAACZ,SAAS;kBACRmK,SAAS;kBACTH,KAAK,EAAC,sBAAmB;kBACzBvG,KAAK,EAAExB,eAAe,CAAC+E,MAAM,IAAI,EAAG;kBACpCoD,UAAU,EAAE;oBAAEC,QAAQ,EAAE;kBAAK,CAAE;kBAC/B7B,OAAO,EAAC;gBAAU;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACP3H,OAAA,CAACf,IAAI;gBAACsJ,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACc,EAAE,EAAE,CAAE;gBAAA/B,QAAA,eACvBvH,OAAA,CAACZ,SAAS;kBACRmK,SAAS;kBACTH,KAAK,EAAC,iBAAiB;kBACvBvG,KAAK,EAAExB,eAAe,CAACqI,YAAY,GACjC,IAAIjF,IAAI,CAACpD,eAAe,CAACqI,YAAY,CAAC,CAACC,kBAAkB,CAAC,OAAO,CAAC,GAAG,EAAG;kBAC1EH,UAAU,EAAE;oBAAEC,QAAQ,EAAE;kBAAK,CAAE;kBAC/B7B,OAAO,EAAC;gBAAU;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACP3H,OAAA,CAACf,IAAI;gBAACsJ,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACc,EAAE,EAAE,CAAE;gBAAA/B,QAAA,eACvBvH,OAAA,CAACZ,SAAS;kBACRmK,SAAS;kBACTH,KAAK,EAAC,QAAQ;kBACdvG,KAAK,EAAE,EAAAvC,sBAAA,GAAAe,eAAe,CAACgD,QAAQ,cAAA/D,sBAAA,uBAAxBA,sBAAA,CAA0BiF,GAAG,KAAI,gBAAiB;kBACzDiE,UAAU,EAAE;oBAAEC,QAAQ,EAAE;kBAAK,CAAE;kBAC/B7B,OAAO,EAAC;gBAAU;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACP3H,OAAA,CAACf,IAAI;gBAACsJ,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACc,EAAE,EAAE,CAAE;gBAAA/B,QAAA,eACvBvH,OAAA,CAACZ,SAAS;kBACRmK,SAAS;kBACTH,KAAK,EAAC,eAAe;kBACrBvG,KAAK,EAAEK,aAAa,CAAC7B,eAAe,CAACkD,KAAK,CAAE;kBAC5CiF,UAAU,EAAE;oBAAEC,QAAQ,EAAE;kBAAK,CAAE;kBAC/B7B,OAAO,EAAC;gBAAU;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,EACNtG,eAAe,CAAC4D,MAAM,IAAI5D,eAAe,CAAC4D,MAAM,CAAC/C,MAAM,GAAG,CAAC,iBAC1DlC,OAAA,CAACf,IAAI;gBAACsJ,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAAAjB,QAAA,gBAChBvH,OAAA,CAACV,UAAU;kBAACsI,OAAO,EAAC,WAAW;kBAACa,YAAY;kBAAAlB,QAAA,EAAC;gBAE7C;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb3H,OAAA,CAAChB,GAAG;kBAACkI,EAAE,EAAE;oBAAEC,OAAO,EAAE,MAAM;oBAAEyC,QAAQ,EAAE,MAAM;oBAAEC,GAAG,EAAE;kBAAE,CAAE;kBAAAtC,QAAA,EACpDlG,eAAe,CAAC4D,MAAM,CAACC,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,kBACvCpF,OAAA,CAACL,IAAI;oBAEHyJ,KAAK,EAAE,GAAGjE,KAAK,CAACK,WAAW,IAAI,SAAS,UAAUL,KAAK,CAACO,QAAQ,GAAI;oBACpEkC,OAAO,EAAC,UAAU;oBAClBkC,IAAI,EAAC;kBAAO,GAHP1E,KAAK;oBAAAoC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAIX,CACF;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CACP;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACP,eAGD3H,OAAA,CAACf,IAAI;QAACsJ,IAAI;QAACC,EAAE,EAAE,EAAG;QAAAjB,QAAA,eAChBvH,OAAA,CAACd,IAAI;UAAAqI,QAAA,eACHvH,OAAA,CAACb,WAAW;YAAAoI,QAAA,gBACVvH,OAAA,CAACV,UAAU;cAACsI,OAAO,EAAC,IAAI;cAACa,YAAY;cAAAlB,QAAA,EAAC;YAEtC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb3H,OAAA,CAACf,IAAI;cAACoJ,SAAS;cAACC,OAAO,EAAE,CAAE;cAAAf,QAAA,eACzBvH,OAAA,CAACf,IAAI;gBAACsJ,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAAAjB,QAAA,eAChBvH,OAAA,CAACT,YAAY;kBACXmJ,OAAO,EAAEzH,QAAS;kBAClB0H,cAAc,EAAGC,MAAM,IAAK,GAAGA,MAAM,CAACmB,MAAM,IAAInB,MAAM,CAACrD,GAAG,EAAE,IAAI,EAAG;kBACnE1C,KAAK,EAAE5B,QAAQ,CAACgC,IAAI,CAAC+G,CAAC,IAAIA,CAAC,CAACtH,GAAG,KAAKnC,QAAQ,CAACE,SAAS,CAAC,IAAI,IAAK;kBAChEqI,QAAQ,EAAEA,CAACC,KAAK,EAAEC,QAAQ,KAAK;oBAC7BrG,YAAY,CAAC,WAAW,EAAE,CAAAqG,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEtG,GAAG,KAAI,EAAE,CAAC;kBAChD,CAAE;kBACFuG,aAAa,EAAEhI,QAAQ,CAACiB,MAAM,KAAK,CAAC,GAAG,0BAA0B,GAAG,sBAAuB;kBAC3FgH,WAAW,EAAGC,MAAM,iBAClBnJ,OAAA,CAACZ,SAAS;oBAAA,GACJ+J,MAAM;oBACVC,KAAK,EAAC,WAAW;oBACjB7G,KAAK,EAAE,CAAC,CAAC5B,MAAM,CAACF,SAAU;oBAC1B4I,UAAU,EAAE1I,MAAM,CAACF,SAAS,KAAKQ,QAAQ,CAACiB,MAAM,KAAK,CAAC,GAAG,0BAA0B,GAAG,EAAE;kBAAE;oBAAAsF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3F;gBACD;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGP3H,OAAA,CAACf,IAAI;QAACsJ,IAAI;QAACC,EAAE,EAAE,EAAG;QAAAjB,QAAA,eAChBvH,OAAA,CAAChB,GAAG;UAACkI,EAAE,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAEC,cAAc,EAAE,UAAU;YAAEyC,GAAG,EAAE,CAAC;YAAE9B,EAAE,EAAE;UAAE,CAAE;UAAAR,QAAA,gBACtEvH,OAAA,CAACX,MAAM;YACLuI,OAAO,EAAC,UAAU;YAClBqC,OAAO,EAAE7J,QAAS;YAClB8J,QAAQ,EAAErJ,OAAQ;YAAA0G,QAAA,EACnB;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT3H,OAAA,CAACX,MAAM;YACL8K,IAAI,EAAC,QAAQ;YACbvC,OAAO,EAAC,WAAW;YACnBsC,QAAQ,EAAErJ,OAAQ;YAAA0G,QAAA,EAEjB1G,OAAO,GAAG,mBAAmB,GAAIX,YAAY,GAAG,UAAU,GAAG;UAAQ;YAAAsH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACtH,EAAA,CApXIJ,gBAAgB;AAAAmK,EAAA,GAAhBnK,gBAAgB;AAsXtB,eAAeA,gBAAgB;AAAC,IAAAmK,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}