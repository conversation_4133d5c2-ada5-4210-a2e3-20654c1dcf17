{"ast": null, "code": "import api from './api';\nconst bonLivraisonService = {\n  // Récupérer tous les bons de livraison\n  getAllBonLivraisons: async (params = {}) => {\n    try {\n      const queryParams = new URLSearchParams();\n      if (params.statut) queryParams.append('statut', params.statut);\n      if (params.livreurId) queryParams.append('livreurId', params.livreurId);\n      if (params.clientId) queryParams.append('clientId', params.clientId);\n      if (params.dateDebut) queryParams.append('dateDebut', params.dateDebut);\n      if (params.dateFin) queryParams.append('dateFin', params.dateFin);\n      if (params.search) queryParams.append('search', params.search);\n      const queryString = queryParams.toString();\n      const url = queryString ? `/bon-livraisons?${queryString}` : '/bon-livraisons';\n      const response = await api.get(url);\n      return response.data;\n    } catch (error) {\n      console.error('Erreur lors de la récupération des bons de livraison:', error);\n      throw error;\n    }\n  },\n  // Récupérer un bon de livraison par ID\n  getBonLivraisonById: async id => {\n    try {\n      const response = await api.get(`/bon-livraisons/${id}`);\n      return response.data;\n    } catch (error) {\n      console.error('Erreur lors de la récupération du bon de livraison:', error);\n      throw error;\n    }\n  },\n  // Créer un nouveau bon de livraison\n  createBonLivraison: async bonLivraisonData => {\n    try {\n      const response = await api.post('/bon-livraisons', bonLivraisonData);\n      return response.data;\n    } catch (error) {\n      console.error('Erreur lors de la création du bon de livraison:', error);\n      throw error;\n    }\n  },\n  // Mettre à jour un bon de livraison\n  updateBonLivraison: async (id, bonLivraisonData) => {\n    try {\n      const response = await api.put(`/bon-livraisons/${id}`, bonLivraisonData);\n      return response.data;\n    } catch (error) {\n      console.error('Erreur lors de la mise à jour du bon de livraison:', error);\n      throw error;\n    }\n  },\n  // Supprimer un bon de livraison\n  deleteBonLivraison: async id => {\n    try {\n      const response = await api.delete(`/bon-livraisons/${id}`);\n      return response.data;\n    } catch (error) {\n      console.error('Erreur lors de la suppression du bon de livraison:', error);\n      throw error;\n    }\n  },\n  // Mettre à jour le statut d'un bon de livraison\n  updateStatutBonLivraison: async (id, statutData) => {\n    try {\n      const response = await api.put(`/bon-livraisons/${id}/statut`, statutData);\n      return response.data;\n    } catch (error) {\n      console.error('Erreur lors de la mise à jour du statut:', error);\n      throw error;\n    }\n  },\n  // Récupérer les statistiques des livraisons\n  getStatistiquesLivraisons: async (params = {}) => {\n    try {\n      const queryParams = new URLSearchParams();\n      if (params.dateDebut) queryParams.append('dateDebut', params.dateDebut);\n      if (params.dateFin) queryParams.append('dateFin', params.dateFin);\n      const queryString = queryParams.toString();\n      const url = queryString ? `/bon-livraisons/statistiques?${queryString}` : '/bon-livraisons/statistiques';\n      const response = await api.get(url);\n      return response.data;\n    } catch (error) {\n      console.error('Erreur lors de la récupération des statistiques:', error);\n      throw error;\n    }\n  },\n  // Valider les données d'un bon de livraison\n  validateBonLivraisonData: bonLivraisonData => {\n    var _bonLivraisonData$adr;\n    const errors = {};\n    if (!bonLivraisonData.clientId) {\n      errors.clientId = 'Le client est obligatoire';\n    }\n    if (!bonLivraisonData.livreurId) {\n      errors.livreurId = 'Le livreur est obligatoire';\n    }\n    if (!bonLivraisonData.dateLivraison) {\n      errors.dateLivraison = 'La date de livraison est obligatoire';\n    }\n    if (!bonLivraisonData.lignes || bonLivraisonData.lignes.length === 0) {\n      errors.lignes = 'Au moins une ligne de produit est obligatoire';\n    } else {\n      // Valider chaque ligne\n      bonLivraisonData.lignes.forEach((ligne, index) => {\n        if (!ligne.produitId) {\n          errors[`ligne_${index}_produit`] = `Le produit est obligatoire pour la ligne ${index + 1}`;\n        }\n        if (!ligne.quantiteLivree || ligne.quantiteLivree <= 0) {\n          errors[`ligne_${index}_quantite`] = `La quantité livrée doit être supérieure à 0 pour la ligne ${index + 1}`;\n        }\n        if (!ligne.quantiteCommandee || ligne.quantiteCommandee <= 0) {\n          errors[`ligne_${index}_commandee`] = `La quantité commandée doit être supérieure à 0 pour la ligne ${index + 1}`;\n        }\n      });\n    }\n    if (!((_bonLivraisonData$adr = bonLivraisonData.adresseLivraison) !== null && _bonLivraisonData$adr !== void 0 && _bonLivraisonData$adr.adresse)) {\n      errors.adresseLivraison = 'L\\'adresse de livraison est obligatoire';\n    }\n    return {\n      isValid: Object.keys(errors).length === 0,\n      errors\n    };\n  },\n  // Formater les données d'un bon de livraison pour l'affichage\n  formatBonLivraisonForDisplay: bonLivraison => {\n    var _bonLivraison$clientI;\n    return {\n      ...bonLivraison,\n      statutLabel: bonLivraisonService.getStatutLabel(bonLivraison.statut),\n      dateLivraisonFormatee: new Date(bonLivraison.dateLivraison).toLocaleDateString('fr-FR'),\n      montantTotalFormate: bonLivraisonService.formatMontant(bonLivraison.montantTotal),\n      pourcentageLivraison: bonLivraisonService.calculatePourcentageLivraison(bonLivraison.lignes),\n      clientNom: ((_bonLivraison$clientI = bonLivraison.clientId) === null || _bonLivraison$clientI === void 0 ? void 0 : _bonLivraison$clientI.nom) || 'Client inconnu',\n      livreurNom: bonLivraison.livreurId ? `${bonLivraison.livreurId.prenom} ${bonLivraison.livreurId.nom}` : 'Livreur inconnu'\n    };\n  },\n  // Calculer le pourcentage de livraison\n  calculatePourcentageLivraison: lignes => {\n    if (!lignes || lignes.length === 0) return 0;\n    const totalCommande = lignes.reduce((sum, ligne) => sum + (ligne.quantiteCommandee || 0), 0);\n    const totalLivre = lignes.reduce((sum, ligne) => sum + (ligne.quantiteLivree || 0), 0);\n    return totalCommande > 0 ? Math.round(totalLivre / totalCommande * 100) : 0;\n  },\n  // Formater un montant\n  formatMontant: montant => {\n    if (!montant) return '0,00 DT';\n    return new Intl.NumberFormat('fr-TN', {\n      style: 'currency',\n      currency: 'TND',\n      minimumFractionDigits: 2\n    }).format(montant).replace('TND', 'DT');\n  },\n  // Obtenir le libellé du statut\n  getStatutLabel: statut => {\n    const statutLabels = {\n      'EN_PREPARATION': 'En préparation',\n      'EN_COURS': 'En cours',\n      'LIVREE': 'Livrée',\n      'PARTIELLEMENT_LIVREE': 'Partiellement livrée',\n      'ECHEC': 'Échec',\n      'ANNULEE': 'Annulée',\n      'RETOURNEE': 'Retournée'\n    };\n    return statutLabels[statut] || statut;\n  },\n  // Obtenir la couleur du statut pour l'affichage\n  getStatutColor: statut => {\n    const statutColors = {\n      'EN_PREPARATION': 'info',\n      'EN_COURS': 'warning',\n      'LIVREE': 'success',\n      'PARTIELLEMENT_LIVREE': 'warning',\n      'ECHEC': 'error',\n      'ANNULEE': 'default',\n      'RETOURNEE': 'secondary'\n    };\n    return statutColors[statut] || 'default';\n  },\n  // Obtenir les options de statut\n  getStatutOptions: () => {\n    return [{\n      value: 'EN_PREPARATION',\n      label: 'En préparation'\n    }, {\n      value: 'EN_COURS',\n      label: 'En cours'\n    }, {\n      value: 'LIVREE',\n      label: 'Livrée'\n    }, {\n      value: 'PARTIELLEMENT_LIVREE',\n      label: 'Partiellement livrée'\n    }, {\n      value: 'ECHEC',\n      label: 'Échec'\n    }, {\n      value: 'ANNULEE',\n      label: 'Annulée'\n    }, {\n      value: 'RETOURNEE',\n      label: 'Retournée'\n    }];\n  },\n  // Vérifier si un bon de livraison peut être modifié\n  canEdit: bonLivraison => {\n    return ['EN_PREPARATION', 'EN_COURS'].includes(bonLivraison.statut);\n  },\n  // Vérifier si un bon de livraison peut être supprimé\n  canDelete: bonLivraison => {\n    return ['EN_PREPARATION', 'ANNULEE'].includes(bonLivraison.statut);\n  },\n  // Créer une ligne de produit vide\n  createEmptyLigne: () => {\n    return {\n      produitId: '',\n      nomProduit: '',\n      description: '',\n      quantiteLivree: 0,\n      quantiteCommandee: 0,\n      unite: 'unité',\n      prixUnitaire: 0,\n      montantLigne: 0,\n      notes: ''\n    };\n  },\n  // Calculer le montant d'une ligne\n  calculateMontantLigne: ligne => {\n    return (ligne.quantiteLivree || 0) * (ligne.prixUnitaire || 0);\n  },\n  // Calculer le montant total d'un bon de livraison\n  calculateMontantTotal: (lignes, tauxTVA = 19) => {\n    const montantHT = lignes.reduce((total, ligne) => {\n      return total + bonLivraisonService.calculateMontantLigne(ligne);\n    }, 0);\n    const montantTTC = montantHT * (1 + tauxTVA / 100);\n    return {\n      montantHT,\n      montantTTC,\n      montantTotal: montantTTC\n    };\n  },\n  // Generate PDF for a bon de livraison\n  generatePdf: async id => {\n    try {\n      const response = await api.get(`/bon-livraisons/${id}/pdf`, {\n        responseType: 'blob'\n      });\n      console.log(\"Response from generatePdf:\", response);\n\n      // Create blob and download\n      const blob = new Blob([response.data], {\n        type: 'application/pdf'\n      });\n      const url = window.URL.createObjectURL(blob);\n      const link = document.createElement('a');\n      link.href = url;\n      link.download = `bon-livraison-${id}.pdf`;\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      window.URL.revokeObjectURL(url);\n      return response.data;\n    } catch (error) {\n      console.error(`Erreur lors de la génération du PDF pour le bon de livraison ${id}:`, error.message);\n      if (error.response) {\n        console.error(\"Réponse du serveur:\", error.response.data);\n        console.error(\"Statut:\", error.response.status);\n      }\n\n      // Afficher une alerte pour informer l'utilisateur\n      alert(`Erreur lors de la génération du PDF: ${error.message}`);\n      throw new Error(`Erreur lors de la génération du PDF: ${error.message}`);\n    }\n  },\n  // Print a bon de livraison\n  print: async id => {\n    try {\n      const response = await api.get(`/bon-livraisons/${id}/print`, {\n        responseType: 'blob'\n      });\n      console.log(\"Response from print:\", response);\n\n      // Create blob and open in new window for printing\n      const blob = new Blob([response.data], {\n        type: 'application/pdf'\n      });\n      const url = window.URL.createObjectURL(blob);\n      const printWindow = window.open(url, '_blank');\n      if (printWindow) {\n        printWindow.onload = () => {\n          printWindow.print();\n        };\n      } else {\n        // Fallback if popup is blocked\n        const link = document.createElement('a');\n        link.href = url;\n        link.target = '_blank';\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n      }\n\n      // Clean up\n      setTimeout(() => {\n        window.URL.revokeObjectURL(url);\n      }, 1000);\n      return response.data;\n    } catch (error) {\n      console.error(`Erreur lors de l'impression du bon de livraison ${id}:`, error.message);\n      if (error.response) {\n        console.error(\"Réponse du serveur:\", error.response.data);\n        console.error(\"Statut:\", error.response.status);\n      }\n\n      // Afficher une alerte pour informer l'utilisateur\n      alert(`Erreur lors de l'impression: ${error.message}`);\n      throw new Error(`Erreur lors de l'impression: ${error.message}`);\n    }\n  },\n  // Send bon de livraison by email\n  sendEmail: async (id, emailData = {}) => {\n    try {\n      const response = await api.post(`/bon-livraisons/${id}/email`, emailData);\n      console.log(\"Response from sendEmail:\", response.data);\n\n      // Afficher une alerte de succès\n      alert(\"Email envoyé avec succès !\");\n      return response.data;\n    } catch (error) {\n      console.error(`Erreur lors de l'envoi de l'email pour le bon de livraison ${id}:`, error.message);\n      if (error.response) {\n        console.error(\"Réponse du serveur:\", error.response.data);\n        console.error(\"Statut:\", error.response.status);\n      }\n\n      // Afficher une alerte pour informer l'utilisateur\n      alert(`Erreur lors de l'envoi de l'email: ${error.message}`);\n      throw new Error(`Erreur lors de l'envoi de l'email: ${error.message}`);\n    }\n  }\n};\n\n// Ajouter la fonction de validation complète\nbonLivraisonService.validateBonLivraisonData = data => {\n  const errors = {};\n\n  // Validation des champs obligatoires\n  if (!data.livreurId) {\n    errors.livreurId = 'Le livreur est obligatoire';\n  }\n  if (!data.factureId) {\n    errors.factureId = 'La facture est obligatoire';\n  }\n  if (!data.clientId) {\n    errors.clientId = 'Le client est obligatoire';\n  }\n  if (!data.dateLivraison) {\n    errors.dateLivraison = 'La date de livraison est obligatoire';\n  }\n  if (!data.lignes || data.lignes.length === 0) {\n    errors.lignes = 'Au moins une ligne de produit est obligatoire';\n  }\n  if (!data.adresseLivraison || !data.adresseLivraison.adresse) {\n    errors.adresseLivraison = 'L\\'adresse de livraison est obligatoire';\n  }\n  if (!data.montantTotal || data.montantTotal <= 0) {\n    errors.montantTotal = 'Le montant total doit être supérieur à 0';\n  }\n  return {\n    isValid: Object.keys(errors).length === 0,\n    errors\n  };\n};\nexport default bonLivraisonService;", "map": {"version": 3, "names": ["api", "bonLivraisonService", "getAllBonLivraisons", "params", "queryParams", "URLSearchParams", "statut", "append", "livreurId", "clientId", "dateDebut", "dateFin", "search", "queryString", "toString", "url", "response", "get", "data", "error", "console", "getBonLivraisonById", "id", "createBonLivraison", "bonLivraisonData", "post", "updateBonLivraison", "put", "deleteBonLivraison", "delete", "updateStatutBonLivraison", "statutData", "getStatistiquesLivraisons", "validateBonLivraisonData", "_bonLivraisonData$adr", "errors", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "lignes", "length", "for<PERSON>ach", "ligne", "index", "produitId", "quantite<PERSON><PERSON><PERSON>", "quantite<PERSON><PERSON><PERSON>ee", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "adresse", "<PERSON><PERSON><PERSON><PERSON>", "Object", "keys", "formatBonLivraisonForDisplay", "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "_bonLivraison$clientI", "statutLabel", "getStatutLabel", "dateLivraisonFormatee", "Date", "toLocaleDateString", "montantTotalFormate", "formatMontant", "montantTotal", "pourcentageLivraison", "calculatePourcentageLivraison", "clientNom", "nom", "livreurNom", "prenom", "totalCommande", "reduce", "sum", "totalLivre", "Math", "round", "montant", "Intl", "NumberFormat", "style", "currency", "minimumFractionDigits", "format", "replace", "statutL<PERSON>ls", "getStatutColor", "statutColors", "getStatutOptions", "value", "label", "canEdit", "includes", "canDelete", "createEmptyLigne", "nomProduit", "description", "unite", "prix<PERSON>ni<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "notes", "calculateMontantLigne", "calculateMontantTotal", "tauxTVA", "montantHT", "total", "montantTTC", "generatePdf", "responseType", "log", "blob", "Blob", "type", "window", "URL", "createObjectURL", "link", "document", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "message", "status", "alert", "Error", "print", "printWindow", "open", "onload", "target", "setTimeout", "sendEmail", "emailData", "factureId"], "sources": ["C:/Users/<USER>/Desktop/Gestion de facture et devis/front-end/src/services/bonLivraisonService.js"], "sourcesContent": ["import api from './api';\n\nconst bonLivraisonService = {\n  // Récupérer tous les bons de livraison\n  getAllBonLivraisons: async (params = {}) => {\n    try {\n      const queryParams = new URLSearchParams();\n\n      if (params.statut) queryParams.append('statut', params.statut);\n      if (params.livreurId) queryParams.append('livreurId', params.livreurId);\n      if (params.clientId) queryParams.append('clientId', params.clientId);\n      if (params.dateDebut) queryParams.append('dateDebut', params.dateDebut);\n      if (params.dateFin) queryParams.append('dateFin', params.dateFin);\n      if (params.search) queryParams.append('search', params.search);\n\n      const queryString = queryParams.toString();\n      const url = queryString ? `/bon-livraisons?${queryString}` : '/bon-livraisons';\n\n      const response = await api.get(url);\n      return response.data;\n    } catch (error) {\n      console.error('Erreur lors de la récupération des bons de livraison:', error);\n      throw error;\n    }\n  },\n\n  // Récupérer un bon de livraison par ID\n  getBonLivraisonById: async (id) => {\n    try {\n      const response = await api.get(`/bon-livraisons/${id}`);\n      return response.data;\n    } catch (error) {\n      console.error('Erreur lors de la récupération du bon de livraison:', error);\n      throw error;\n    }\n  },\n\n  // Créer un nouveau bon de livraison\n  createBonLivraison: async (bonLivraisonData) => {\n    try {\n      const response = await api.post('/bon-livraisons', bonLivraisonData);\n      return response.data;\n    } catch (error) {\n      console.error('Erreur lors de la création du bon de livraison:', error);\n      throw error;\n    }\n  },\n\n  // Mettre à jour un bon de livraison\n  updateBonLivraison: async (id, bonLivraisonData) => {\n    try {\n      const response = await api.put(`/bon-livraisons/${id}`, bonLivraisonData);\n      return response.data;\n    } catch (error) {\n      console.error('Erreur lors de la mise à jour du bon de livraison:', error);\n      throw error;\n    }\n  },\n\n  // Supprimer un bon de livraison\n  deleteBonLivraison: async (id) => {\n    try {\n      const response = await api.delete(`/bon-livraisons/${id}`);\n      return response.data;\n    } catch (error) {\n      console.error('Erreur lors de la suppression du bon de livraison:', error);\n      throw error;\n    }\n  },\n\n  // Mettre à jour le statut d'un bon de livraison\n  updateStatutBonLivraison: async (id, statutData) => {\n    try {\n      const response = await api.put(`/bon-livraisons/${id}/statut`, statutData);\n      return response.data;\n    } catch (error) {\n      console.error('Erreur lors de la mise à jour du statut:', error);\n      throw error;\n    }\n  },\n\n  // Récupérer les statistiques des livraisons\n  getStatistiquesLivraisons: async (params = {}) => {\n    try {\n      const queryParams = new URLSearchParams();\n\n      if (params.dateDebut) queryParams.append('dateDebut', params.dateDebut);\n      if (params.dateFin) queryParams.append('dateFin', params.dateFin);\n\n      const queryString = queryParams.toString();\n      const url = queryString ? `/bon-livraisons/statistiques?${queryString}` : '/bon-livraisons/statistiques';\n\n      const response = await api.get(url);\n      return response.data;\n    } catch (error) {\n      console.error('Erreur lors de la récupération des statistiques:', error);\n      throw error;\n    }\n  },\n\n  // Valider les données d'un bon de livraison\n  validateBonLivraisonData: (bonLivraisonData) => {\n    const errors = {};\n\n    if (!bonLivraisonData.clientId) {\n      errors.clientId = 'Le client est obligatoire';\n    }\n\n    if (!bonLivraisonData.livreurId) {\n      errors.livreurId = 'Le livreur est obligatoire';\n    }\n\n    if (!bonLivraisonData.dateLivraison) {\n      errors.dateLivraison = 'La date de livraison est obligatoire';\n    }\n\n    if (!bonLivraisonData.lignes || bonLivraisonData.lignes.length === 0) {\n      errors.lignes = 'Au moins une ligne de produit est obligatoire';\n    } else {\n      // Valider chaque ligne\n      bonLivraisonData.lignes.forEach((ligne, index) => {\n        if (!ligne.produitId) {\n          errors[`ligne_${index}_produit`] = `Le produit est obligatoire pour la ligne ${index + 1}`;\n        }\n        if (!ligne.quantiteLivree || ligne.quantiteLivree <= 0) {\n          errors[`ligne_${index}_quantite`] = `La quantité livrée doit être supérieure à 0 pour la ligne ${index + 1}`;\n        }\n        if (!ligne.quantiteCommandee || ligne.quantiteCommandee <= 0) {\n          errors[`ligne_${index}_commandee`] = `La quantité commandée doit être supérieure à 0 pour la ligne ${index + 1}`;\n        }\n      });\n    }\n\n    if (!bonLivraisonData.adresseLivraison?.adresse) {\n      errors.adresseLivraison = 'L\\'adresse de livraison est obligatoire';\n    }\n\n    return {\n      isValid: Object.keys(errors).length === 0,\n      errors\n    };\n  },\n\n  // Formater les données d'un bon de livraison pour l'affichage\n  formatBonLivraisonForDisplay: (bonLivraison) => {\n    return {\n      ...bonLivraison,\n      statutLabel: bonLivraisonService.getStatutLabel(bonLivraison.statut),\n      dateLivraisonFormatee: new Date(bonLivraison.dateLivraison).toLocaleDateString('fr-FR'),\n      montantTotalFormate: bonLivraisonService.formatMontant(bonLivraison.montantTotal),\n      pourcentageLivraison: bonLivraisonService.calculatePourcentageLivraison(bonLivraison.lignes),\n      clientNom: bonLivraison.clientId?.nom || 'Client inconnu',\n      livreurNom: bonLivraison.livreurId ?\n        `${bonLivraison.livreurId.prenom} ${bonLivraison.livreurId.nom}` :\n        'Livreur inconnu'\n    };\n  },\n\n  // Calculer le pourcentage de livraison\n  calculatePourcentageLivraison: (lignes) => {\n    if (!lignes || lignes.length === 0) return 0;\n\n    const totalCommande = lignes.reduce((sum, ligne) => sum + (ligne.quantiteCommandee || 0), 0);\n    const totalLivre = lignes.reduce((sum, ligne) => sum + (ligne.quantiteLivree || 0), 0);\n\n    return totalCommande > 0 ? Math.round((totalLivre / totalCommande) * 100) : 0;\n  },\n\n  // Formater un montant\n  formatMontant: (montant) => {\n    if (!montant) return '0,00 DT';\n    return new Intl.NumberFormat('fr-TN', {\n      style: 'currency',\n      currency: 'TND',\n      minimumFractionDigits: 2\n    }).format(montant).replace('TND', 'DT');\n  },\n\n  // Obtenir le libellé du statut\n  getStatutLabel: (statut) => {\n    const statutLabels = {\n      'EN_PREPARATION': 'En préparation',\n      'EN_COURS': 'En cours',\n      'LIVREE': 'Livrée',\n      'PARTIELLEMENT_LIVREE': 'Partiellement livrée',\n      'ECHEC': 'Échec',\n      'ANNULEE': 'Annulée',\n      'RETOURNEE': 'Retournée'\n    };\n    return statutLabels[statut] || statut;\n  },\n\n  // Obtenir la couleur du statut pour l'affichage\n  getStatutColor: (statut) => {\n    const statutColors = {\n      'EN_PREPARATION': 'info',\n      'EN_COURS': 'warning',\n      'LIVREE': 'success',\n      'PARTIELLEMENT_LIVREE': 'warning',\n      'ECHEC': 'error',\n      'ANNULEE': 'default',\n      'RETOURNEE': 'secondary'\n    };\n    return statutColors[statut] || 'default';\n  },\n\n  // Obtenir les options de statut\n  getStatutOptions: () => {\n    return [\n      { value: 'EN_PREPARATION', label: 'En préparation' },\n      { value: 'EN_COURS', label: 'En cours' },\n      { value: 'LIVREE', label: 'Livrée' },\n      { value: 'PARTIELLEMENT_LIVREE', label: 'Partiellement livrée' },\n      { value: 'ECHEC', label: 'Échec' },\n      { value: 'ANNULEE', label: 'Annulée' },\n      { value: 'RETOURNEE', label: 'Retournée' }\n    ];\n  },\n\n  // Vérifier si un bon de livraison peut être modifié\n  canEdit: (bonLivraison) => {\n    return ['EN_PREPARATION', 'EN_COURS'].includes(bonLivraison.statut);\n  },\n\n  // Vérifier si un bon de livraison peut être supprimé\n  canDelete: (bonLivraison) => {\n    return ['EN_PREPARATION', 'ANNULEE'].includes(bonLivraison.statut);\n  },\n\n  // Créer une ligne de produit vide\n  createEmptyLigne: () => {\n    return {\n      produitId: '',\n      nomProduit: '',\n      description: '',\n      quantiteLivree: 0,\n      quantiteCommandee: 0,\n      unite: 'unité',\n      prixUnitaire: 0,\n      montantLigne: 0,\n      notes: ''\n    };\n  },\n\n  // Calculer le montant d'une ligne\n  calculateMontantLigne: (ligne) => {\n    return (ligne.quantiteLivree || 0) * (ligne.prixUnitaire || 0);\n  },\n\n  // Calculer le montant total d'un bon de livraison\n  calculateMontantTotal: (lignes, tauxTVA = 19) => {\n    const montantHT = lignes.reduce((total, ligne) => {\n      return total + bonLivraisonService.calculateMontantLigne(ligne);\n    }, 0);\n\n    const montantTTC = montantHT * (1 + tauxTVA / 100);\n\n    return {\n      montantHT,\n      montantTTC,\n      montantTotal: montantTTC\n    };\n  },\n\n  // Generate PDF for a bon de livraison\n  generatePdf: async (id) => {\n    try {\n      const response = await api.get(`/bon-livraisons/${id}/pdf`, {\n        responseType: 'blob'\n      });\n      console.log(\"Response from generatePdf:\", response);\n\n      // Create blob and download\n      const blob = new Blob([response.data], { type: 'application/pdf' });\n      const url = window.URL.createObjectURL(blob);\n      const link = document.createElement('a');\n      link.href = url;\n      link.download = `bon-livraison-${id}.pdf`;\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      window.URL.revokeObjectURL(url);\n\n      return response.data;\n    } catch (error) {\n      console.error(`Erreur lors de la génération du PDF pour le bon de livraison ${id}:`, error.message);\n      if (error.response) {\n        console.error(\"Réponse du serveur:\", error.response.data);\n        console.error(\"Statut:\", error.response.status);\n      }\n\n      // Afficher une alerte pour informer l'utilisateur\n      alert(`Erreur lors de la génération du PDF: ${error.message}`);\n\n      throw new Error(`Erreur lors de la génération du PDF: ${error.message}`);\n    }\n  },\n\n  // Print a bon de livraison\n  print: async (id) => {\n    try {\n      const response = await api.get(`/bon-livraisons/${id}/print`, {\n        responseType: 'blob'\n      });\n      console.log(\"Response from print:\", response);\n\n      // Create blob and open in new window for printing\n      const blob = new Blob([response.data], { type: 'application/pdf' });\n      const url = window.URL.createObjectURL(blob);\n      const printWindow = window.open(url, '_blank');\n\n      if (printWindow) {\n        printWindow.onload = () => {\n          printWindow.print();\n        };\n      } else {\n        // Fallback if popup is blocked\n        const link = document.createElement('a');\n        link.href = url;\n        link.target = '_blank';\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n      }\n\n      // Clean up\n      setTimeout(() => {\n        window.URL.revokeObjectURL(url);\n      }, 1000);\n\n      return response.data;\n    } catch (error) {\n      console.error(`Erreur lors de l'impression du bon de livraison ${id}:`, error.message);\n      if (error.response) {\n        console.error(\"Réponse du serveur:\", error.response.data);\n        console.error(\"Statut:\", error.response.status);\n      }\n\n      // Afficher une alerte pour informer l'utilisateur\n      alert(`Erreur lors de l'impression: ${error.message}`);\n\n      throw new Error(`Erreur lors de l'impression: ${error.message}`);\n    }\n  },\n\n  // Send bon de livraison by email\n  sendEmail: async (id, emailData = {}) => {\n    try {\n      const response = await api.post(`/bon-livraisons/${id}/email`, emailData);\n      console.log(\"Response from sendEmail:\", response.data);\n\n      // Afficher une alerte de succès\n      alert(\"Email envoyé avec succès !\");\n\n      return response.data;\n    } catch (error) {\n      console.error(`Erreur lors de l'envoi de l'email pour le bon de livraison ${id}:`, error.message);\n      if (error.response) {\n        console.error(\"Réponse du serveur:\", error.response.data);\n        console.error(\"Statut:\", error.response.status);\n      }\n\n      // Afficher une alerte pour informer l'utilisateur\n      alert(`Erreur lors de l'envoi de l'email: ${error.message}`);\n\n      throw new Error(`Erreur lors de l'envoi de l'email: ${error.message}`);\n    }\n  }\n};\n\n// Ajouter la fonction de validation complète\nbonLivraisonService.validateBonLivraisonData = (data) => {\n  const errors = {};\n\n  // Validation des champs obligatoires\n  if (!data.livreurId) {\n    errors.livreurId = 'Le livreur est obligatoire';\n  }\n\n  if (!data.factureId) {\n    errors.factureId = 'La facture est obligatoire';\n  }\n\n  if (!data.clientId) {\n    errors.clientId = 'Le client est obligatoire';\n  }\n\n  if (!data.dateLivraison) {\n    errors.dateLivraison = 'La date de livraison est obligatoire';\n  }\n\n  if (!data.lignes || data.lignes.length === 0) {\n    errors.lignes = 'Au moins une ligne de produit est obligatoire';\n  }\n\n  if (!data.adresseLivraison || !data.adresseLivraison.adresse) {\n    errors.adresseLivraison = 'L\\'adresse de livraison est obligatoire';\n  }\n\n  if (!data.montantTotal || data.montantTotal <= 0) {\n    errors.montantTotal = 'Le montant total doit être supérieur à 0';\n  }\n\n  return {\n    isValid: Object.keys(errors).length === 0,\n    errors\n  };\n};\n\nexport default bonLivraisonService;\n"], "mappings": "AAAA,OAAOA,GAAG,MAAM,OAAO;AAEvB,MAAMC,mBAAmB,GAAG;EAC1B;EACAC,mBAAmB,EAAE,MAAAA,CAAOC,MAAM,GAAG,CAAC,CAAC,KAAK;IAC1C,IAAI;MACF,MAAMC,WAAW,GAAG,IAAIC,eAAe,CAAC,CAAC;MAEzC,IAAIF,MAAM,CAACG,MAAM,EAAEF,WAAW,CAACG,MAAM,CAAC,QAAQ,EAAEJ,MAAM,CAACG,MAAM,CAAC;MAC9D,IAAIH,MAAM,CAACK,SAAS,EAAEJ,WAAW,CAACG,MAAM,CAAC,WAAW,EAAEJ,MAAM,CAACK,SAAS,CAAC;MACvE,IAAIL,MAAM,CAACM,QAAQ,EAAEL,WAAW,CAACG,MAAM,CAAC,UAAU,EAAEJ,MAAM,CAACM,QAAQ,CAAC;MACpE,IAAIN,MAAM,CAACO,SAAS,EAAEN,WAAW,CAACG,MAAM,CAAC,WAAW,EAAEJ,MAAM,CAACO,SAAS,CAAC;MACvE,IAAIP,MAAM,CAACQ,OAAO,EAAEP,WAAW,CAACG,MAAM,CAAC,SAAS,EAAEJ,MAAM,CAACQ,OAAO,CAAC;MACjE,IAAIR,MAAM,CAACS,MAAM,EAAER,WAAW,CAACG,MAAM,CAAC,QAAQ,EAAEJ,MAAM,CAACS,MAAM,CAAC;MAE9D,MAAMC,WAAW,GAAGT,WAAW,CAACU,QAAQ,CAAC,CAAC;MAC1C,MAAMC,GAAG,GAAGF,WAAW,GAAG,mBAAmBA,WAAW,EAAE,GAAG,iBAAiB;MAE9E,MAAMG,QAAQ,GAAG,MAAMhB,GAAG,CAACiB,GAAG,CAACF,GAAG,CAAC;MACnC,OAAOC,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uDAAuD,EAAEA,KAAK,CAAC;MAC7E,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACAE,mBAAmB,EAAE,MAAOC,EAAE,IAAK;IACjC,IAAI;MACF,MAAMN,QAAQ,GAAG,MAAMhB,GAAG,CAACiB,GAAG,CAAC,mBAAmBK,EAAE,EAAE,CAAC;MACvD,OAAON,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,qDAAqD,EAAEA,KAAK,CAAC;MAC3E,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACAI,kBAAkB,EAAE,MAAOC,gBAAgB,IAAK;IAC9C,IAAI;MACF,MAAMR,QAAQ,GAAG,MAAMhB,GAAG,CAACyB,IAAI,CAAC,iBAAiB,EAAED,gBAAgB,CAAC;MACpE,OAAOR,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,iDAAiD,EAAEA,KAAK,CAAC;MACvE,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACAO,kBAAkB,EAAE,MAAAA,CAAOJ,EAAE,EAAEE,gBAAgB,KAAK;IAClD,IAAI;MACF,MAAMR,QAAQ,GAAG,MAAMhB,GAAG,CAAC2B,GAAG,CAAC,mBAAmBL,EAAE,EAAE,EAAEE,gBAAgB,CAAC;MACzE,OAAOR,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,oDAAoD,EAAEA,KAAK,CAAC;MAC1E,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACAS,kBAAkB,EAAE,MAAON,EAAE,IAAK;IAChC,IAAI;MACF,MAAMN,QAAQ,GAAG,MAAMhB,GAAG,CAAC6B,MAAM,CAAC,mBAAmBP,EAAE,EAAE,CAAC;MAC1D,OAAON,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,oDAAoD,EAAEA,KAAK,CAAC;MAC1E,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACAW,wBAAwB,EAAE,MAAAA,CAAOR,EAAE,EAAES,UAAU,KAAK;IAClD,IAAI;MACF,MAAMf,QAAQ,GAAG,MAAMhB,GAAG,CAAC2B,GAAG,CAAC,mBAAmBL,EAAE,SAAS,EAAES,UAAU,CAAC;MAC1E,OAAOf,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;MAChE,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACAa,yBAAyB,EAAE,MAAAA,CAAO7B,MAAM,GAAG,CAAC,CAAC,KAAK;IAChD,IAAI;MACF,MAAMC,WAAW,GAAG,IAAIC,eAAe,CAAC,CAAC;MAEzC,IAAIF,MAAM,CAACO,SAAS,EAAEN,WAAW,CAACG,MAAM,CAAC,WAAW,EAAEJ,MAAM,CAACO,SAAS,CAAC;MACvE,IAAIP,MAAM,CAACQ,OAAO,EAAEP,WAAW,CAACG,MAAM,CAAC,SAAS,EAAEJ,MAAM,CAACQ,OAAO,CAAC;MAEjE,MAAME,WAAW,GAAGT,WAAW,CAACU,QAAQ,CAAC,CAAC;MAC1C,MAAMC,GAAG,GAAGF,WAAW,GAAG,gCAAgCA,WAAW,EAAE,GAAG,8BAA8B;MAExG,MAAMG,QAAQ,GAAG,MAAMhB,GAAG,CAACiB,GAAG,CAACF,GAAG,CAAC;MACnC,OAAOC,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,kDAAkD,EAAEA,KAAK,CAAC;MACxE,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACAc,wBAAwB,EAAGT,gBAAgB,IAAK;IAAA,IAAAU,qBAAA;IAC9C,MAAMC,MAAM,GAAG,CAAC,CAAC;IAEjB,IAAI,CAACX,gBAAgB,CAACf,QAAQ,EAAE;MAC9B0B,MAAM,CAAC1B,QAAQ,GAAG,2BAA2B;IAC/C;IAEA,IAAI,CAACe,gBAAgB,CAAChB,SAAS,EAAE;MAC/B2B,MAAM,CAAC3B,SAAS,GAAG,4BAA4B;IACjD;IAEA,IAAI,CAACgB,gBAAgB,CAACY,aAAa,EAAE;MACnCD,MAAM,CAACC,aAAa,GAAG,sCAAsC;IAC/D;IAEA,IAAI,CAACZ,gBAAgB,CAACa,MAAM,IAAIb,gBAAgB,CAACa,MAAM,CAACC,MAAM,KAAK,CAAC,EAAE;MACpEH,MAAM,CAACE,MAAM,GAAG,+CAA+C;IACjE,CAAC,MAAM;MACL;MACAb,gBAAgB,CAACa,MAAM,CAACE,OAAO,CAAC,CAACC,KAAK,EAAEC,KAAK,KAAK;QAChD,IAAI,CAACD,KAAK,CAACE,SAAS,EAAE;UACpBP,MAAM,CAAC,SAASM,KAAK,UAAU,CAAC,GAAG,4CAA4CA,KAAK,GAAG,CAAC,EAAE;QAC5F;QACA,IAAI,CAACD,KAAK,CAACG,cAAc,IAAIH,KAAK,CAACG,cAAc,IAAI,CAAC,EAAE;UACtDR,MAAM,CAAC,SAASM,KAAK,WAAW,CAAC,GAAG,6DAA6DA,KAAK,GAAG,CAAC,EAAE;QAC9G;QACA,IAAI,CAACD,KAAK,CAACI,iBAAiB,IAAIJ,KAAK,CAACI,iBAAiB,IAAI,CAAC,EAAE;UAC5DT,MAAM,CAAC,SAASM,KAAK,YAAY,CAAC,GAAG,gEAAgEA,KAAK,GAAG,CAAC,EAAE;QAClH;MACF,CAAC,CAAC;IACJ;IAEA,IAAI,GAAAP,qBAAA,GAACV,gBAAgB,CAACqB,gBAAgB,cAAAX,qBAAA,eAAjCA,qBAAA,CAAmCY,OAAO,GAAE;MAC/CX,MAAM,CAACU,gBAAgB,GAAG,yCAAyC;IACrE;IAEA,OAAO;MACLE,OAAO,EAAEC,MAAM,CAACC,IAAI,CAACd,MAAM,CAAC,CAACG,MAAM,KAAK,CAAC;MACzCH;IACF,CAAC;EACH,CAAC;EAED;EACAe,4BAA4B,EAAGC,YAAY,IAAK;IAAA,IAAAC,qBAAA;IAC9C,OAAO;MACL,GAAGD,YAAY;MACfE,WAAW,EAAEpD,mBAAmB,CAACqD,cAAc,CAACH,YAAY,CAAC7C,MAAM,CAAC;MACpEiD,qBAAqB,EAAE,IAAIC,IAAI,CAACL,YAAY,CAACf,aAAa,CAAC,CAACqB,kBAAkB,CAAC,OAAO,CAAC;MACvFC,mBAAmB,EAAEzD,mBAAmB,CAAC0D,aAAa,CAACR,YAAY,CAACS,YAAY,CAAC;MACjFC,oBAAoB,EAAE5D,mBAAmB,CAAC6D,6BAA6B,CAACX,YAAY,CAACd,MAAM,CAAC;MAC5F0B,SAAS,EAAE,EAAAX,qBAAA,GAAAD,YAAY,CAAC1C,QAAQ,cAAA2C,qBAAA,uBAArBA,qBAAA,CAAuBY,GAAG,KAAI,gBAAgB;MACzDC,UAAU,EAAEd,YAAY,CAAC3C,SAAS,GAChC,GAAG2C,YAAY,CAAC3C,SAAS,CAAC0D,MAAM,IAAIf,YAAY,CAAC3C,SAAS,CAACwD,GAAG,EAAE,GAChE;IACJ,CAAC;EACH,CAAC;EAED;EACAF,6BAA6B,EAAGzB,MAAM,IAAK;IACzC,IAAI,CAACA,MAAM,IAAIA,MAAM,CAACC,MAAM,KAAK,CAAC,EAAE,OAAO,CAAC;IAE5C,MAAM6B,aAAa,GAAG9B,MAAM,CAAC+B,MAAM,CAAC,CAACC,GAAG,EAAE7B,KAAK,KAAK6B,GAAG,IAAI7B,KAAK,CAACI,iBAAiB,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;IAC5F,MAAM0B,UAAU,GAAGjC,MAAM,CAAC+B,MAAM,CAAC,CAACC,GAAG,EAAE7B,KAAK,KAAK6B,GAAG,IAAI7B,KAAK,CAACG,cAAc,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;IAEtF,OAAOwB,aAAa,GAAG,CAAC,GAAGI,IAAI,CAACC,KAAK,CAAEF,UAAU,GAAGH,aAAa,GAAI,GAAG,CAAC,GAAG,CAAC;EAC/E,CAAC;EAED;EACAR,aAAa,EAAGc,OAAO,IAAK;IAC1B,IAAI,CAACA,OAAO,EAAE,OAAO,SAAS;IAC9B,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE,KAAK;MACfC,qBAAqB,EAAE;IACzB,CAAC,CAAC,CAACC,MAAM,CAACN,OAAO,CAAC,CAACO,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC;EACzC,CAAC;EAED;EACA1B,cAAc,EAAGhD,MAAM,IAAK;IAC1B,MAAM2E,YAAY,GAAG;MACnB,gBAAgB,EAAE,gBAAgB;MAClC,UAAU,EAAE,UAAU;MACtB,QAAQ,EAAE,QAAQ;MAClB,sBAAsB,EAAE,sBAAsB;MAC9C,OAAO,EAAE,OAAO;MAChB,SAAS,EAAE,SAAS;MACpB,WAAW,EAAE;IACf,CAAC;IACD,OAAOA,YAAY,CAAC3E,MAAM,CAAC,IAAIA,MAAM;EACvC,CAAC;EAED;EACA4E,cAAc,EAAG5E,MAAM,IAAK;IAC1B,MAAM6E,YAAY,GAAG;MACnB,gBAAgB,EAAE,MAAM;MACxB,UAAU,EAAE,SAAS;MACrB,QAAQ,EAAE,SAAS;MACnB,sBAAsB,EAAE,SAAS;MACjC,OAAO,EAAE,OAAO;MAChB,SAAS,EAAE,SAAS;MACpB,WAAW,EAAE;IACf,CAAC;IACD,OAAOA,YAAY,CAAC7E,MAAM,CAAC,IAAI,SAAS;EAC1C,CAAC;EAED;EACA8E,gBAAgB,EAAEA,CAAA,KAAM;IACtB,OAAO,CACL;MAAEC,KAAK,EAAE,gBAAgB;MAAEC,KAAK,EAAE;IAAiB,CAAC,EACpD;MAAED,KAAK,EAAE,UAAU;MAAEC,KAAK,EAAE;IAAW,CAAC,EACxC;MAAED,KAAK,EAAE,QAAQ;MAAEC,KAAK,EAAE;IAAS,CAAC,EACpC;MAAED,KAAK,EAAE,sBAAsB;MAAEC,KAAK,EAAE;IAAuB,CAAC,EAChE;MAAED,KAAK,EAAE,OAAO;MAAEC,KAAK,EAAE;IAAQ,CAAC,EAClC;MAAED,KAAK,EAAE,SAAS;MAAEC,KAAK,EAAE;IAAU,CAAC,EACtC;MAAED,KAAK,EAAE,WAAW;MAAEC,KAAK,EAAE;IAAY,CAAC,CAC3C;EACH,CAAC;EAED;EACAC,OAAO,EAAGpC,YAAY,IAAK;IACzB,OAAO,CAAC,gBAAgB,EAAE,UAAU,CAAC,CAACqC,QAAQ,CAACrC,YAAY,CAAC7C,MAAM,CAAC;EACrE,CAAC;EAED;EACAmF,SAAS,EAAGtC,YAAY,IAAK;IAC3B,OAAO,CAAC,gBAAgB,EAAE,SAAS,CAAC,CAACqC,QAAQ,CAACrC,YAAY,CAAC7C,MAAM,CAAC;EACpE,CAAC;EAED;EACAoF,gBAAgB,EAAEA,CAAA,KAAM;IACtB,OAAO;MACLhD,SAAS,EAAE,EAAE;MACbiD,UAAU,EAAE,EAAE;MACdC,WAAW,EAAE,EAAE;MACfjD,cAAc,EAAE,CAAC;MACjBC,iBAAiB,EAAE,CAAC;MACpBiD,KAAK,EAAE,OAAO;MACdC,YAAY,EAAE,CAAC;MACfC,YAAY,EAAE,CAAC;MACfC,KAAK,EAAE;IACT,CAAC;EACH,CAAC;EAED;EACAC,qBAAqB,EAAGzD,KAAK,IAAK;IAChC,OAAO,CAACA,KAAK,CAACG,cAAc,IAAI,CAAC,KAAKH,KAAK,CAACsD,YAAY,IAAI,CAAC,CAAC;EAChE,CAAC;EAED;EACAI,qBAAqB,EAAEA,CAAC7D,MAAM,EAAE8D,OAAO,GAAG,EAAE,KAAK;IAC/C,MAAMC,SAAS,GAAG/D,MAAM,CAAC+B,MAAM,CAAC,CAACiC,KAAK,EAAE7D,KAAK,KAAK;MAChD,OAAO6D,KAAK,GAAGpG,mBAAmB,CAACgG,qBAAqB,CAACzD,KAAK,CAAC;IACjE,CAAC,EAAE,CAAC,CAAC;IAEL,MAAM8D,UAAU,GAAGF,SAAS,IAAI,CAAC,GAAGD,OAAO,GAAG,GAAG,CAAC;IAElD,OAAO;MACLC,SAAS;MACTE,UAAU;MACV1C,YAAY,EAAE0C;IAChB,CAAC;EACH,CAAC;EAED;EACAC,WAAW,EAAE,MAAOjF,EAAE,IAAK;IACzB,IAAI;MACF,MAAMN,QAAQ,GAAG,MAAMhB,GAAG,CAACiB,GAAG,CAAC,mBAAmBK,EAAE,MAAM,EAAE;QAC1DkF,YAAY,EAAE;MAChB,CAAC,CAAC;MACFpF,OAAO,CAACqF,GAAG,CAAC,4BAA4B,EAAEzF,QAAQ,CAAC;;MAEnD;MACA,MAAM0F,IAAI,GAAG,IAAIC,IAAI,CAAC,CAAC3F,QAAQ,CAACE,IAAI,CAAC,EAAE;QAAE0F,IAAI,EAAE;MAAkB,CAAC,CAAC;MACnE,MAAM7F,GAAG,GAAG8F,MAAM,CAACC,GAAG,CAACC,eAAe,CAACL,IAAI,CAAC;MAC5C,MAAMM,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACxCF,IAAI,CAACG,IAAI,GAAGpG,GAAG;MACfiG,IAAI,CAACI,QAAQ,GAAG,iBAAiB9F,EAAE,MAAM;MACzC2F,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,IAAI,CAAC;MAC/BA,IAAI,CAACO,KAAK,CAAC,CAAC;MACZN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACR,IAAI,CAAC;MAC/BH,MAAM,CAACC,GAAG,CAACW,eAAe,CAAC1G,GAAG,CAAC;MAE/B,OAAOC,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,gEAAgEG,EAAE,GAAG,EAAEH,KAAK,CAACuG,OAAO,CAAC;MACnG,IAAIvG,KAAK,CAACH,QAAQ,EAAE;QAClBI,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAACH,QAAQ,CAACE,IAAI,CAAC;QACzDE,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAACH,QAAQ,CAAC2G,MAAM,CAAC;MACjD;;MAEA;MACAC,KAAK,CAAC,wCAAwCzG,KAAK,CAACuG,OAAO,EAAE,CAAC;MAE9D,MAAM,IAAIG,KAAK,CAAC,wCAAwC1G,KAAK,CAACuG,OAAO,EAAE,CAAC;IAC1E;EACF,CAAC;EAED;EACAI,KAAK,EAAE,MAAOxG,EAAE,IAAK;IACnB,IAAI;MACF,MAAMN,QAAQ,GAAG,MAAMhB,GAAG,CAACiB,GAAG,CAAC,mBAAmBK,EAAE,QAAQ,EAAE;QAC5DkF,YAAY,EAAE;MAChB,CAAC,CAAC;MACFpF,OAAO,CAACqF,GAAG,CAAC,sBAAsB,EAAEzF,QAAQ,CAAC;;MAE7C;MACA,MAAM0F,IAAI,GAAG,IAAIC,IAAI,CAAC,CAAC3F,QAAQ,CAACE,IAAI,CAAC,EAAE;QAAE0F,IAAI,EAAE;MAAkB,CAAC,CAAC;MACnE,MAAM7F,GAAG,GAAG8F,MAAM,CAACC,GAAG,CAACC,eAAe,CAACL,IAAI,CAAC;MAC5C,MAAMqB,WAAW,GAAGlB,MAAM,CAACmB,IAAI,CAACjH,GAAG,EAAE,QAAQ,CAAC;MAE9C,IAAIgH,WAAW,EAAE;QACfA,WAAW,CAACE,MAAM,GAAG,MAAM;UACzBF,WAAW,CAACD,KAAK,CAAC,CAAC;QACrB,CAAC;MACH,CAAC,MAAM;QACL;QACA,MAAMd,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;QACxCF,IAAI,CAACG,IAAI,GAAGpG,GAAG;QACfiG,IAAI,CAACkB,MAAM,GAAG,QAAQ;QACtBjB,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,IAAI,CAAC;QAC/BA,IAAI,CAACO,KAAK,CAAC,CAAC;QACZN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACR,IAAI,CAAC;MACjC;;MAEA;MACAmB,UAAU,CAAC,MAAM;QACftB,MAAM,CAACC,GAAG,CAACW,eAAe,CAAC1G,GAAG,CAAC;MACjC,CAAC,EAAE,IAAI,CAAC;MAER,OAAOC,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,mDAAmDG,EAAE,GAAG,EAAEH,KAAK,CAACuG,OAAO,CAAC;MACtF,IAAIvG,KAAK,CAACH,QAAQ,EAAE;QAClBI,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAACH,QAAQ,CAACE,IAAI,CAAC;QACzDE,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAACH,QAAQ,CAAC2G,MAAM,CAAC;MACjD;;MAEA;MACAC,KAAK,CAAC,gCAAgCzG,KAAK,CAACuG,OAAO,EAAE,CAAC;MAEtD,MAAM,IAAIG,KAAK,CAAC,gCAAgC1G,KAAK,CAACuG,OAAO,EAAE,CAAC;IAClE;EACF,CAAC;EAED;EACAU,SAAS,EAAE,MAAAA,CAAO9G,EAAE,EAAE+G,SAAS,GAAG,CAAC,CAAC,KAAK;IACvC,IAAI;MACF,MAAMrH,QAAQ,GAAG,MAAMhB,GAAG,CAACyB,IAAI,CAAC,mBAAmBH,EAAE,QAAQ,EAAE+G,SAAS,CAAC;MACzEjH,OAAO,CAACqF,GAAG,CAAC,0BAA0B,EAAEzF,QAAQ,CAACE,IAAI,CAAC;;MAEtD;MACA0G,KAAK,CAAC,4BAA4B,CAAC;MAEnC,OAAO5G,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8DAA8DG,EAAE,GAAG,EAAEH,KAAK,CAACuG,OAAO,CAAC;MACjG,IAAIvG,KAAK,CAACH,QAAQ,EAAE;QAClBI,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAACH,QAAQ,CAACE,IAAI,CAAC;QACzDE,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAACH,QAAQ,CAAC2G,MAAM,CAAC;MACjD;;MAEA;MACAC,KAAK,CAAC,sCAAsCzG,KAAK,CAACuG,OAAO,EAAE,CAAC;MAE5D,MAAM,IAAIG,KAAK,CAAC,sCAAsC1G,KAAK,CAACuG,OAAO,EAAE,CAAC;IACxE;EACF;AACF,CAAC;;AAED;AACAzH,mBAAmB,CAACgC,wBAAwB,GAAIf,IAAI,IAAK;EACvD,MAAMiB,MAAM,GAAG,CAAC,CAAC;;EAEjB;EACA,IAAI,CAACjB,IAAI,CAACV,SAAS,EAAE;IACnB2B,MAAM,CAAC3B,SAAS,GAAG,4BAA4B;EACjD;EAEA,IAAI,CAACU,IAAI,CAACoH,SAAS,EAAE;IACnBnG,MAAM,CAACmG,SAAS,GAAG,4BAA4B;EACjD;EAEA,IAAI,CAACpH,IAAI,CAACT,QAAQ,EAAE;IAClB0B,MAAM,CAAC1B,QAAQ,GAAG,2BAA2B;EAC/C;EAEA,IAAI,CAACS,IAAI,CAACkB,aAAa,EAAE;IACvBD,MAAM,CAACC,aAAa,GAAG,sCAAsC;EAC/D;EAEA,IAAI,CAAClB,IAAI,CAACmB,MAAM,IAAInB,IAAI,CAACmB,MAAM,CAACC,MAAM,KAAK,CAAC,EAAE;IAC5CH,MAAM,CAACE,MAAM,GAAG,+CAA+C;EACjE;EAEA,IAAI,CAACnB,IAAI,CAAC2B,gBAAgB,IAAI,CAAC3B,IAAI,CAAC2B,gBAAgB,CAACC,OAAO,EAAE;IAC5DX,MAAM,CAACU,gBAAgB,GAAG,yCAAyC;EACrE;EAEA,IAAI,CAAC3B,IAAI,CAAC0C,YAAY,IAAI1C,IAAI,CAAC0C,YAAY,IAAI,CAAC,EAAE;IAChDzB,MAAM,CAACyB,YAAY,GAAG,0CAA0C;EAClE;EAEA,OAAO;IACLb,OAAO,EAAEC,MAAM,CAACC,IAAI,CAACd,MAAM,CAAC,CAACG,MAAM,KAAK,CAAC;IACzCH;EACF,CAAC;AACH,CAAC;AAED,eAAelC,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}