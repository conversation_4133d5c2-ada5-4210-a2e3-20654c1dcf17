[{"C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\index.jsx": "1", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\App.jsx": "2", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\styles\\theme.js": "3", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\components\\Sidebar.jsx": "4", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\pages\\Dashboard.jsx": "5", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\pages\\Factures.jsx": "6", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\pages\\Clients.jsx": "7", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\pages\\Parametres.jsx": "8", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\pages\\Templates.jsx": "9", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\pages\\Devis.jsx": "10", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\pages\\Produits.jsx": "11", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\components\\PaiementModal.jsx": "12", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\components\\ClientForm.jsx": "13", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\components\\FactureCard.jsx": "14", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\components\\TemplateSelector.jsx": "15", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\services\\devisService.js": "16", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\services\\factureService.js": "17", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\services\\clientService.js": "18", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\services\\entrepriseService.js": "19", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\utils\\formatters.js": "20", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\services\\produitService.js": "21", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\components\\DevisCard.jsx": "22", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\components\\ProduitForm.jsx": "23", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\components\\StatutBadge.jsx": "24", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\services\\api.js": "25", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\components\\FactureForm.jsx": "26", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\components\\DevisForm.jsx": "27", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\components\\ColorPicker.jsx": "28", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\components\\DocumentPreview.jsx": "29", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\components\\TopBar.jsx": "30", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\contexts\\AuthContext.jsx": "31", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\services\\authService.js": "32", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\utils\\authUtils.js": "33", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\pages\\ForgotPassword.jsx": "34", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\pages\\Login.jsx": "35", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\pages\\SignUp.jsx": "36", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\layouts\\AuthLayout.jsx": "37", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\styles\\cegid-theme.js": "38", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\pages\\UserProfile.jsx": "39", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\stores\\companyStore.js": "40", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\services\\parametresService.js": "41", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\pages\\AccessDenied.jsx": "42", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\layouts\\AdminLayout.jsx": "43", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\layouts\\ClientLayout.jsx": "44", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\components\\AdminSidebar.jsx": "45", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\components\\ClientSidebar.jsx": "46", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\components\\ClientTopBar.jsx": "47", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\components\\AdminTopBar.jsx": "48", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\components\\TemplateCustomizer.jsx": "49", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\hooks\\useTemplate.js": "50", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\services\\templateService.js": "51", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\layouts\\VendeurLayout.jsx": "52", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\components\\VendeurTopBar.jsx": "53", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\components\\VendeurSidebar.jsx": "54", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\services\\vendeurService.js": "55", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\pages\\UsersPage.jsx": "56", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\pages\\UserForm.jsx": "57", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\pages\\VendeursPage.jsx": "58", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\pages\\EntreprisesPage.jsx": "59", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\pages\\ParametresAdmin.jsx": "60", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\services\\userService.js": "61", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\pages\\AdminLogin.jsx": "62", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\contexts\\LanguageContext.jsx": "63", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\utils\\translations.js": "64", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\components\\LanguageSelector.jsx": "65", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\pages\\EntreprisePaiements.jsx": "66", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\pages\\EntrepriseDocumentView.jsx": "67", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\services\\paiementService.js": "68", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\components\\EmailDialog.jsx": "69", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\components\\PowerBIExport.jsx": "70", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\utils\\csvExporter.js": "71", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\pages\\TemplateSettings.jsx": "72", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\components\\StockEvolution.jsx": "73", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\components\\ClientCard.jsx": "74", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\components\\ClientDetail.jsx": "75", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\pages\\VendeurPaiements.jsx": "76", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\components\\PaiementReceiptDialog.jsx": "77", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\components\\PaiementReceipt.jsx": "78", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\pages\\ResetPassword.jsx": "79", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\components\\AdminUserMetrics.jsx": "80", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\pages\\PowerBIDashboard.jsx": "81", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\config\\constants.js": "82", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\pages\\VendeurBIDashboard.jsx": "83", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\services\\analyticsService.js": "84", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\components\\DateRangeSelector.jsx": "85", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\pages\\AdminAnalyticsDashboard.jsx": "86", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\services\\userMetricsService.js": "87", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\pages\\EntrepriseBIDashboard.jsx": "88", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\pages\\EquipeManagement.jsx": "89", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\components\\SimpleDateFilter.jsx": "90", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\pages\\ClientDashboard.jsx": "91", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\components\\ClientFactureView.jsx": "92", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\pages\\ClientFactures.jsx": "93", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\pages\\ClientDevis.jsx": "94", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\components\\ClientDevisView.jsx": "95", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\pages\\ClientProfile.jsx": "96", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\services\\profileService.js": "97", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\pages\\ClientPaiements.jsx": "98", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\pages\\VerifyOTP.jsx": "99", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\layouts\\ResponsableLayout.jsx": "100", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\components\\ClientDemandeDevisForm.jsx": "101", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\components\\ResponsableDevisValidationForm.jsx": "102", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\components\\admin\\abonnements\\AbonnementsList.jsx": "103", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\components\\admin\\abonnements\\AbonnementDetails.jsx": "104", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\components\\admin\\abonnements\\AbonnementForm.jsx": "105", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\services\\abonnementService.js": "106", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\services\\responsableEntrepriseService.js": "107", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\pages\\ResponsableAbonnementPage.jsx": "108", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\components\\responsable\\abonnement\\AbonnementStatus.jsx": "109", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\components\\responsable\\abonnement\\AbonnementRenewal.jsx": "110", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\components\\responsable\\abonnement\\SubscriptionExpirationAlert.jsx": "111", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\components\\responsable\\abonnement\\AbonnementTimer.jsx": "112", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\components\\admin\\abonnements\\RenewalRequestsBadge.jsx": "113", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\components\\admin\\abonnements\\RenewalRequestsList.jsx": "114", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\components\\TemplateManager.jsx": "115", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\stores\\templateStore.js": "116", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\pages\\ResponsableTemplateCustomization.jsx": "117", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\pages\\AdminTemplateManagement.jsx": "118", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\services\\responsableTemplateService.js": "119", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\services\\baseTemplateService.js": "120", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\pages\\BonLivraison.jsx": "121", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\pages\\Livreurs.jsx": "122", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\services\\bonLivraisonService.js": "123", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\components\\LivreurForm.jsx": "124", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\services\\livreurService.js": "125", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\components\\BonLivraisonForm.jsx": "126", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\pages\\Entreprise.jsx": "127", "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\components\\BonLivraisonViewDialog.jsx": "128"}, {"size": 346, "mtime": 1747999456369, "results": "129", "hashOfConfig": "130"}, {"size": 36093, "mtime": 1748260117357, "results": "131", "hashOfConfig": "130"}, {"size": 3604, "mtime": 1742836006540, "results": "132", "hashOfConfig": "130"}, {"size": 13082, "mtime": 1744953130481, "results": "133", "hashOfConfig": "130"}, {"size": 39667, "mtime": 1744360878817, "results": "134", "hashOfConfig": "130"}, {"size": 26112, "mtime": 1747770315711, "results": "135", "hashOfConfig": "130"}, {"size": 47276, "mtime": 1747781065617, "results": "136", "hashOfConfig": "130"}, {"size": 10813, "mtime": 1745259524321, "results": "137", "hashOfConfig": "130"}, {"size": 44108, "mtime": 1745842393629, "results": "138", "hashOfConfig": "130"}, {"size": 32617, "mtime": 1747785060543, "results": "139", "hashOfConfig": "130"}, {"size": 28307, "mtime": 1746812429924, "results": "140", "hashOfConfig": "130"}, {"size": 5686, "mtime": 1746812007663, "results": "141", "hashOfConfig": "130"}, {"size": 25684, "mtime": 1747783852334, "results": "142", "hashOfConfig": "130"}, {"size": 13200, "mtime": 1746838682882, "results": "143", "hashOfConfig": "130"}, {"size": 1160, "mtime": 1742835316031, "results": "144", "hashOfConfig": "130"}, {"size": 14571, "mtime": 1747771271885, "results": "145", "hashOfConfig": "130"}, {"size": 12351, "mtime": 1747661913906, "results": "146", "hashOfConfig": "130"}, {"size": 4243, "mtime": 1747948324940, "results": "147", "hashOfConfig": "130"}, {"size": 6383, "mtime": 1747509467794, "results": "148", "hashOfConfig": "130"}, {"size": 8674, "mtime": 1747837868993, "results": "149", "hashOfConfig": "130"}, {"size": 7445, "mtime": 1747948370386, "results": "150", "hashOfConfig": "130"}, {"size": 12511, "mtime": 1747785086544, "results": "151", "hashOfConfig": "130"}, {"size": 11972, "mtime": 1746811984617, "results": "152", "hashOfConfig": "130"}, {"size": 978, "mtime": 1747784890375, "results": "153", "hashOfConfig": "130"}, {"size": 4309, "mtime": 1747912400339, "results": "154", "hashOfConfig": "130"}, {"size": 98149, "mtime": 1747784295975, "results": "155", "hashOfConfig": "130"}, {"size": 98774, "mtime": 1747784169056, "results": "156", "hashOfConfig": "130"}, {"size": 1292, "mtime": 1744359232000, "results": "157", "hashOfConfig": "130"}, {"size": 14587, "mtime": 1747453962794, "results": "158", "hashOfConfig": "130"}, {"size": 19868, "mtime": 1744954370969, "results": "159", "hashOfConfig": "130"}, {"size": 3481, "mtime": 1747626973966, "results": "160", "hashOfConfig": "130"}, {"size": 9464, "mtime": 1747627131949, "results": "161", "hashOfConfig": "130"}, {"size": 4582, "mtime": 1747768841645, "results": "162", "hashOfConfig": "130"}, {"size": 12099, "mtime": 1747626628335, "results": "163", "hashOfConfig": "130"}, {"size": 16787, "mtime": 1748259661349, "results": "164", "hashOfConfig": "130"}, {"size": 32962, "mtime": 1747780764608, "results": "165", "hashOfConfig": "130"}, {"size": 345, "mtime": 1744359232000, "results": "166", "hashOfConfig": "130"}, {"size": 6513, "mtime": 1744360239000, "results": "167", "hashOfConfig": "130"}, {"size": 29235, "mtime": 1745759626325, "results": "168", "hashOfConfig": "130"}, {"size": 1076, "mtime": 1744769558687, "results": "169", "hashOfConfig": "130"}, {"size": 4529, "mtime": 1747837632674, "results": "170", "hashOfConfig": "130"}, {"size": 4306, "mtime": 1747627078934, "results": "171", "hashOfConfig": "130"}, {"size": 1067, "mtime": 1748233294076, "results": "172", "hashOfConfig": "130"}, {"size": 1100, "mtime": 1748233294088, "results": "173", "hashOfConfig": "130"}, {"size": 17955, "mtime": 1747934233933, "results": "174", "hashOfConfig": "130"}, {"size": 29025, "mtime": 1748003462916, "results": "175", "hashOfConfig": "130"}, {"size": 5277, "mtime": 1747507208228, "results": "176", "hashOfConfig": "130"}, {"size": 6059, "mtime": 1746841670355, "results": "177", "hashOfConfig": "130"}, {"size": 21312, "mtime": 1745794481659, "results": "178", "hashOfConfig": "130"}, {"size": 3103, "mtime": 1747943526290, "results": "179", "hashOfConfig": "130"}, {"size": 6917, "mtime": 1747914905419, "results": "180", "hashOfConfig": "130"}, {"size": 1085, "mtime": 1748233294079, "results": "181", "hashOfConfig": "130"}, {"size": 6963, "mtime": 1747231601133, "results": "182", "hashOfConfig": "130"}, {"size": 19674, "mtime": 1748002745934, "results": "183", "hashOfConfig": "130"}, {"size": 2839, "mtime": 1745644664390, "results": "184", "hashOfConfig": "130"}, {"size": 14988, "mtime": 1747454703566, "results": "185", "hashOfConfig": "130"}, {"size": 28307, "mtime": 1747839110335, "results": "186", "hashOfConfig": "130"}, {"size": 20755, "mtime": 1747657662956, "results": "187", "hashOfConfig": "130"}, {"size": 21662, "mtime": 1747768251992, "results": "188", "hashOfConfig": "130"}, {"size": 15737, "mtime": 1747837603217, "results": "189", "hashOfConfig": "130"}, {"size": 10943, "mtime": 1747765088197, "results": "190", "hashOfConfig": "130"}, {"size": 14698, "mtime": 1746621678836, "results": "191", "hashOfConfig": "130"}, {"size": 3282, "mtime": 1745761770321, "results": "192", "hashOfConfig": "130"}, {"size": 10950, "mtime": 1746531357679, "results": "193", "hashOfConfig": "130"}, {"size": 2767, "mtime": 1745671877511, "results": "194", "hashOfConfig": "130"}, {"size": 43898, "mtime": 1747660886391, "results": "195", "hashOfConfig": "130"}, {"size": 22521, "mtime": 1747187647521, "results": "196", "hashOfConfig": "130"}, {"size": 3805, "mtime": 1746809762621, "results": "197", "hashOfConfig": "130"}, {"size": 3445, "mtime": 1746618449225, "results": "198", "hashOfConfig": "130"}, {"size": 7790, "mtime": 1748000547895, "results": "199", "hashOfConfig": "130"}, {"size": 2353, "mtime": 1747443498206, "results": "200", "hashOfConfig": "130"}, {"size": 14305, "mtime": 1747930918009, "results": "201", "hashOfConfig": "130"}, {"size": 18459, "mtime": 1746812561850, "results": "202", "hashOfConfig": "130"}, {"size": 9375, "mtime": 1747781012238, "results": "203", "hashOfConfig": "130"}, {"size": 20984, "mtime": 1747662874052, "results": "204", "hashOfConfig": "130"}, {"size": 28845, "mtime": 1747621647508, "results": "205", "hashOfConfig": "130"}, {"size": 3085, "mtime": 1746809736216, "results": "206", "hashOfConfig": "130"}, {"size": 10337, "mtime": 1747621415084, "results": "207", "hashOfConfig": "130"}, {"size": 16333, "mtime": 1747042765407, "results": "208", "hashOfConfig": "130"}, {"size": 13184, "mtime": 1747077863875, "results": "209", "hashOfConfig": "130"}, {"size": 41520, "mtime": 1747456802814, "results": "210", "hashOfConfig": "130"}, {"size": 645, "mtime": 1747191810148, "results": "211", "hashOfConfig": "130"}, {"size": 39404, "mtime": 1748000549305, "results": "212", "hashOfConfig": "130"}, {"size": 14147, "mtime": 1747656825802, "results": "213", "hashOfConfig": "130"}, {"size": 4978, "mtime": 1747243693339, "results": "214", "hashOfConfig": "130"}, {"size": 21887, "mtime": 1747656901012, "results": "215", "hashOfConfig": "130"}, {"size": 4628, "mtime": 1747446227098, "results": "216", "hashOfConfig": "130"}, {"size": 40467, "mtime": 1747656878737, "results": "217", "hashOfConfig": "130"}, {"size": 50360, "mtime": 1747839138262, "results": "218", "hashOfConfig": "130"}, {"size": 10016, "mtime": 1747659101712, "results": "219", "hashOfConfig": "130"}, {"size": 45720, "mtime": 1748000550754, "results": "220", "hashOfConfig": "130"}, {"size": 17549, "mtime": 1747614815265, "results": "221", "hashOfConfig": "130"}, {"size": 21706, "mtime": 1747659518502, "results": "222", "hashOfConfig": "130"}, {"size": 28582, "mtime": 1747771518204, "results": "223", "hashOfConfig": "130"}, {"size": 19154, "mtime": 1747773914568, "results": "224", "hashOfConfig": "130"}, {"size": 25243, "mtime": 1747663640256, "results": "225", "hashOfConfig": "130"}, {"size": 2487, "mtime": 1747508029494, "results": "226", "hashOfConfig": "130"}, {"size": 27104, "mtime": 1747621582530, "results": "227", "hashOfConfig": "130"}, {"size": 17666, "mtime": 1747626958055, "results": "228", "hashOfConfig": "130"}, {"size": 1304, "mtime": 1748233294083, "results": "229", "hashOfConfig": "130"}, {"size": 9565, "mtime": 1747785376765, "results": "230", "hashOfConfig": "130"}, {"size": 12382, "mtime": 1747771428250, "results": "231", "hashOfConfig": "130"}, {"size": 9387, "mtime": 1747828157003, "results": "232", "hashOfConfig": "130"}, {"size": 8687, "mtime": 1747780156492, "results": "233", "hashOfConfig": "130"}, {"size": 21996, "mtime": 1747829923584, "results": "234", "hashOfConfig": "130"}, {"size": 4979, "mtime": 1747828026369, "results": "235", "hashOfConfig": "130"}, {"size": 2760, "mtime": 1747777875907, "results": "236", "hashOfConfig": "130"}, {"size": 2511, "mtime": 1747781851327, "results": "237", "hashOfConfig": "130"}, {"size": 7786, "mtime": 1747829973951, "results": "238", "hashOfConfig": "130"}, {"size": 5646, "mtime": 1747829936579, "results": "239", "hashOfConfig": "130"}, {"size": 6211, "mtime": 1747887654749, "results": "240", "hashOfConfig": "130"}, {"size": 4616, "mtime": 1747827512151, "results": "241", "hashOfConfig": "130"}, {"size": 1253, "mtime": 1747828041009, "results": "242", "hashOfConfig": "130"}, {"size": 13008, "mtime": 1747828524315, "results": "243", "hashOfConfig": "130"}, {"size": 22214, "mtime": 1747885901603, "results": "244", "hashOfConfig": "130"}, {"size": 6759, "mtime": 1747886022724, "results": "245", "hashOfConfig": "130"}, {"size": 38627, "mtime": 1747942052368, "results": "246", "hashOfConfig": "130"}, {"size": 20450, "mtime": 1747943077761, "results": "247", "hashOfConfig": "130"}, {"size": 5203, "mtime": 1747944068531, "results": "248", "hashOfConfig": "130"}, {"size": 3798, "mtime": 1747933816248, "results": "249", "hashOfConfig": "130"}, {"size": 26622, "mtime": 1748010129316, "results": "250", "hashOfConfig": "130"}, {"size": 13796, "mtime": 1747946758875, "results": "251", "hashOfConfig": "130"}, {"size": 13238, "mtime": 1748435172373, "results": "252", "hashOfConfig": "130"}, {"size": 10433, "mtime": 1747946797997, "results": "253", "hashOfConfig": "130"}, {"size": 5947, "mtime": 1747951770223, "results": "254", "hashOfConfig": "130"}, {"size": 13798, "mtime": 1748435437968, "results": "255", "hashOfConfig": "130"}, {"size": 5598, "mtime": 1748003937362, "results": "256", "hashOfConfig": "130"}, {"size": 10006, "mtime": 1748008789514, "results": "257", "hashOfConfig": "130"}, {"filePath": "258", "messages": "259", "suppressedMessages": "260", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1lqc98n", {"filePath": "261", "messages": "262", "suppressedMessages": "263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "264", "messages": "265", "suppressedMessages": "266", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "267", "messages": "268", "suppressedMessages": "269", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "270", "messages": "271", "suppressedMessages": "272", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "273", "messages": "274", "suppressedMessages": "275", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "276", "messages": "277", "suppressedMessages": "278", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 16, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "279", "messages": "280", "suppressedMessages": "281", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "282", "messages": "283", "suppressedMessages": "284", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "285", "messages": "286", "suppressedMessages": "287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "288", "messages": "289", "suppressedMessages": "290", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "291", "messages": "292", "suppressedMessages": "293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "294", "messages": "295", "suppressedMessages": "296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "297", "messages": "298", "suppressedMessages": "299", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "300", "messages": "301", "suppressedMessages": "302", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "303", "messages": "304", "suppressedMessages": "305", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "306", "messages": "307", "suppressedMessages": "308", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "309", "messages": "310", "suppressedMessages": "311", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "312", "messages": "313", "suppressedMessages": "314", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "315", "messages": "316", "suppressedMessages": "317", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "318", "messages": "319", "suppressedMessages": "320", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "321", "messages": "322", "suppressedMessages": "323", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "324", "messages": "325", "suppressedMessages": "326", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "327", "messages": "328", "suppressedMessages": "329", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "330", "messages": "331", "suppressedMessages": "332", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "333", "messages": "334", "suppressedMessages": "335", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "336", "messages": "337", "suppressedMessages": "338", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "339", "messages": "340", "suppressedMessages": "341", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "342", "messages": "343", "suppressedMessages": "344", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "345", "messages": "346", "suppressedMessages": "347", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "348", "messages": "349", "suppressedMessages": "350", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "351", "messages": "352", "suppressedMessages": "353", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "354", "messages": "355", "suppressedMessages": "356", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "357", "messages": "358", "suppressedMessages": "359", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "360", "messages": "361", "suppressedMessages": "362", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "363", "messages": "364", "suppressedMessages": "365", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "366", "messages": "367", "suppressedMessages": "368", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "369", "messages": "370", "suppressedMessages": "371", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "372", "messages": "373", "suppressedMessages": "374", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "375", "messages": "376", "suppressedMessages": "377", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "378", "messages": "379", "suppressedMessages": "380", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "381", "messages": "382", "suppressedMessages": "383", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "384", "messages": "385", "suppressedMessages": "386", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "387", "messages": "388", "suppressedMessages": "389", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "390", "messages": "391", "suppressedMessages": "392", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "393", "messages": "394", "suppressedMessages": "395", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "396", "messages": "397", "suppressedMessages": "398", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "399", "messages": "400", "suppressedMessages": "401", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "402", "messages": "403", "suppressedMessages": "404", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "405", "messages": "406", "suppressedMessages": "407", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "408", "messages": "409", "suppressedMessages": "410", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "411", "messages": "412", "suppressedMessages": "413", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "414", "messages": "415", "suppressedMessages": "416", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "417", "messages": "418", "suppressedMessages": "419", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "420", "messages": "421", "suppressedMessages": "422", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "423", "messages": "424", "suppressedMessages": "425", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "426", "messages": "427", "suppressedMessages": "428", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "429", "messages": "430", "suppressedMessages": "431", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 19, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "432", "messages": "433", "suppressedMessages": "434", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 17, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "435", "messages": "436", "suppressedMessages": "437", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "438", "messages": "439", "suppressedMessages": "440", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "441", "messages": "442", "suppressedMessages": "443", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "444", "messages": "445", "suppressedMessages": "446", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "447", "messages": "448", "suppressedMessages": "449", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "450", "messages": "451", "suppressedMessages": "452", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "453", "messages": "454", "suppressedMessages": "455", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "456", "messages": "457", "suppressedMessages": "458", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "459", "messages": "460", "suppressedMessages": "461", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "462", "messages": "463", "suppressedMessages": "464", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "465", "messages": "466", "suppressedMessages": "467", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "468", "messages": "469", "suppressedMessages": "470", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "471", "messages": "472", "suppressedMessages": "473", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "474", "messages": "475", "suppressedMessages": "476", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "477", "messages": "478", "suppressedMessages": "479", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "480", "messages": "481", "suppressedMessages": "482", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "483", "messages": "484", "suppressedMessages": "485", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 17, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "486", "messages": "487", "suppressedMessages": "488", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "489", "messages": "490", "suppressedMessages": "491", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "492", "messages": "493", "suppressedMessages": "494", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "495", "messages": "496", "suppressedMessages": "497", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "498", "messages": "499", "suppressedMessages": "500", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 27, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "501", "messages": "502", "suppressedMessages": "503", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "504", "messages": "505", "suppressedMessages": "506", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 21, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "507", "messages": "508", "suppressedMessages": "509", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "510", "messages": "511", "suppressedMessages": "512", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "513", "messages": "514", "suppressedMessages": "515", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "516", "messages": "517", "suppressedMessages": "518", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "519", "messages": "520", "suppressedMessages": "521", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 22, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "522", "messages": "523", "suppressedMessages": "524", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "525", "messages": "526", "suppressedMessages": "527", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "528", "messages": "529", "suppressedMessages": "530", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 28, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "531", "messages": "532", "suppressedMessages": "533", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "534", "messages": "535", "suppressedMessages": "536", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "537", "messages": "538", "suppressedMessages": "539", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "540", "messages": "541", "suppressedMessages": "542", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "543", "messages": "544", "suppressedMessages": "545", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "546", "messages": "547", "suppressedMessages": "548", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "549", "messages": "550", "suppressedMessages": "551", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 30, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "552", "messages": "553", "suppressedMessages": "554", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "555", "messages": "556", "suppressedMessages": "557", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "558", "messages": "559", "suppressedMessages": "560", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "561", "messages": "562", "suppressedMessages": "563", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "564", "messages": "565", "suppressedMessages": "566", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "567", "messages": "568", "suppressedMessages": "569", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "570", "messages": "571", "suppressedMessages": "572", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "573", "messages": "574", "suppressedMessages": "575", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "576", "messages": "577", "suppressedMessages": "578", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "579", "messages": "580", "suppressedMessages": "581", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "582", "messages": "583", "suppressedMessages": "584", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "585", "messages": "586", "suppressedMessages": "587", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "588", "messages": "589", "suppressedMessages": "590", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "591", "messages": "592", "suppressedMessages": "593", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "594", "messages": "595", "suppressedMessages": "596", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "597", "messages": "598", "suppressedMessages": "599", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "600", "messages": "601", "suppressedMessages": "602", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "603", "messages": "604", "suppressedMessages": "605", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "606", "messages": "607", "suppressedMessages": "608", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "609", "messages": "610", "suppressedMessages": "611", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "612", "messages": "613", "suppressedMessages": "614", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "615", "messages": "616", "suppressedMessages": "617", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "618", "messages": "619", "suppressedMessages": "620", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "621", "messages": "622", "suppressedMessages": "623", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "624", "messages": "625", "suppressedMessages": "626", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "627", "messages": "628", "suppressedMessages": "629", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "630", "messages": "631", "suppressedMessages": "632", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "633", "messages": "634", "suppressedMessages": "635", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "636", "messages": "637", "suppressedMessages": "638", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "639", "messages": "640", "suppressedMessages": "641", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\index.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\App.jsx", ["642", "643", "644"], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\styles\\theme.js", [], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\components\\Sidebar.jsx", ["645", "646"], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\pages\\Dashboard.jsx", ["647", "648", "649", "650", "651", "652", "653", "654"], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\pages\\Factures.jsx", ["655", "656", "657", "658", "659", "660", "661", "662", "663", "664", "665"], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\pages\\Clients.jsx", ["666", "667", "668", "669", "670", "671", "672", "673", "674", "675", "676", "677", "678", "679", "680", "681"], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\pages\\Parametres.jsx", ["682"], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\pages\\Templates.jsx", ["683", "684"], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\pages\\Devis.jsx", ["685", "686", "687", "688", "689", "690", "691", "692"], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\pages\\Produits.jsx", ["693", "694", "695", "696", "697", "698", "699"], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\components\\PaiementModal.jsx", ["700", "701"], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\components\\ClientForm.jsx", ["702", "703", "704", "705", "706", "707"], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\components\\FactureCard.jsx", ["708"], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\components\\TemplateSelector.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\services\\devisService.js", ["709", "710", "711"], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\services\\factureService.js", ["712", "713", "714"], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\services\\clientService.js", [], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\services\\entrepriseService.js", [], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\utils\\formatters.js", [], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\services\\produitService.js", ["715"], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\components\\DevisCard.jsx", ["716"], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\components\\ProduitForm.jsx", ["717"], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\components\\StatutBadge.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\services\\api.js", [], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\components\\FactureForm.jsx", ["718", "719"], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\components\\DevisForm.jsx", ["720", "721", "722", "723", "724", "725"], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\components\\ColorPicker.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\components\\DocumentPreview.jsx", ["726", "727", "728"], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\components\\TopBar.jsx", ["729"], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\contexts\\AuthContext.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\services\\authService.js", ["730", "731"], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\utils\\authUtils.js", [], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\pages\\ForgotPassword.jsx", ["732", "733", "734"], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\pages\\Login.jsx", ["735", "736", "737"], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\pages\\SignUp.jsx", ["738", "739", "740", "741", "742", "743", "744", "745"], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\layouts\\AuthLayout.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\styles\\cegid-theme.js", [], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\pages\\UserProfile.jsx", ["746", "747"], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\stores\\companyStore.js", [], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\services\\parametresService.js", [], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\pages\\AccessDenied.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\layouts\\AdminLayout.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\layouts\\ClientLayout.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\components\\AdminSidebar.jsx", ["748", "749", "750", "751"], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\components\\ClientSidebar.jsx", ["752", "753", "754", "755", "756"], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\components\\ClientTopBar.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\components\\AdminTopBar.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\components\\TemplateCustomizer.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\hooks\\useTemplate.js", [], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\services\\templateService.js", [], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\layouts\\VendeurLayout.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\components\\VendeurTopBar.jsx", ["757", "758", "759", "760", "761", "762"], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\components\\VendeurSidebar.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\services\\vendeurService.js", [], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\pages\\UsersPage.jsx", ["763", "764", "765", "766", "767", "768", "769", "770", "771", "772", "773"], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\pages\\UserForm.jsx", ["774", "775", "776", "777", "778"], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\pages\\VendeursPage.jsx", ["779", "780", "781", "782", "783", "784", "785", "786", "787", "788", "789", "790", "791", "792", "793", "794", "795", "796", "797"], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\pages\\EntreprisesPage.jsx", ["798", "799", "800", "801", "802", "803", "804", "805", "806", "807", "808", "809", "810", "811", "812", "813", "814"], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\pages\\ParametresAdmin.jsx", ["815", "816", "817", "818", "819", "820", "821", "822", "823", "824", "825"], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\services\\userService.js", ["826", "827", "828"], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\pages\\AdminLogin.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\contexts\\LanguageContext.jsx", ["829"], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\utils\\translations.js", ["830"], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\components\\LanguageSelector.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\pages\\EntreprisePaiements.jsx", ["831", "832", "833", "834", "835", "836", "837", "838", "839", "840", "841", "842"], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\pages\\EntrepriseDocumentView.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\services\\paiementService.js", [], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\components\\EmailDialog.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\components\\PowerBIExport.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\utils\\csvExporter.js", [], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\pages\\TemplateSettings.jsx", ["843"], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\components\\StockEvolution.jsx", ["844", "845", "846", "847", "848", "849", "850"], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\components\\ClientCard.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\components\\ClientDetail.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\pages\\VendeurPaiements.jsx", ["851", "852", "853", "854", "855", "856", "857", "858", "859", "860", "861", "862", "863", "864", "865", "866", "867"], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\components\\PaiementReceiptDialog.jsx", ["868"], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\components\\PaiementReceipt.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\pages\\ResetPassword.jsx", ["869", "870"], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\components\\AdminUserMetrics.jsx", ["871", "872", "873", "874", "875", "876", "877", "878", "879", "880"], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\pages\\PowerBIDashboard.jsx", ["881", "882", "883", "884", "885", "886", "887", "888", "889", "890", "891", "892", "893", "894", "895", "896", "897", "898", "899", "900", "901", "902", "903", "904", "905", "906", "907"], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\config\\constants.js", [], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\pages\\VendeurBIDashboard.jsx", ["908", "909", "910", "911", "912", "913", "914", "915", "916", "917", "918", "919", "920", "921", "922", "923", "924", "925", "926", "927", "928"], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\services\\analyticsService.js", [], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\components\\DateRangeSelector.jsx", ["929", "930"], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\pages\\AdminAnalyticsDashboard.jsx", ["931"], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\services\\userMetricsService.js", [], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\pages\\EntrepriseBIDashboard.jsx", ["932", "933", "934", "935", "936", "937", "938", "939", "940", "941", "942", "943", "944", "945", "946", "947", "948", "949", "950", "951", "952", "953"], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\pages\\EquipeManagement.jsx", ["954", "955", "956", "957", "958", "959", "960", "961"], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\components\\SimpleDateFilter.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\pages\\ClientDashboard.jsx", ["962", "963", "964", "965", "966", "967", "968", "969", "970", "971", "972", "973", "974", "975", "976", "977", "978", "979", "980", "981", "982", "983", "984", "985", "986", "987", "988", "989"], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\components\\ClientFactureView.jsx", ["990", "991", "992", "993"], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\pages\\ClientFactures.jsx", ["994", "995", "996", "997", "998", "999", "1000", "1001"], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\pages\\ClientDevis.jsx", ["1002", "1003", "1004", "1005"], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\components\\ClientDevisView.jsx", ["1006", "1007", "1008", "1009"], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\pages\\ClientProfile.jsx", ["1010", "1011", "1012", "1013", "1014", "1015", "1016", "1017", "1018", "1019"], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\services\\profileService.js", [], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\pages\\ClientPaiements.jsx", ["1020", "1021", "1022", "1023", "1024", "1025", "1026", "1027", "1028", "1029", "1030", "1031", "1032", "1033", "1034", "1035", "1036", "1037", "1038", "1039", "1040", "1041", "1042", "1043", "1044", "1045", "1046", "1047", "1048", "1049"], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\pages\\VerifyOTP.jsx", ["1050"], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\layouts\\ResponsableLayout.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\components\\ClientDemandeDevisForm.jsx", ["1051", "1052", "1053", "1054", "1055"], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\components\\ResponsableDevisValidationForm.jsx", ["1056", "1057", "1058", "1059", "1060"], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\components\\admin\\abonnements\\AbonnementsList.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\components\\admin\\abonnements\\AbonnementDetails.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\components\\admin\\abonnements\\AbonnementForm.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\services\\abonnementService.js", [], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\services\\responsableEntrepriseService.js", [], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\pages\\ResponsableAbonnementPage.jsx", ["1061"], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\components\\responsable\\abonnement\\AbonnementStatus.jsx", ["1062", "1063", "1064"], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\components\\responsable\\abonnement\\AbonnementRenewal.jsx", ["1065", "1066", "1067"], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\components\\responsable\\abonnement\\SubscriptionExpirationAlert.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\components\\responsable\\abonnement\\AbonnementTimer.jsx", ["1068"], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\components\\admin\\abonnements\\RenewalRequestsBadge.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\components\\admin\\abonnements\\RenewalRequestsList.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\components\\TemplateManager.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\stores\\templateStore.js", [], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\pages\\ResponsableTemplateCustomization.jsx", ["1069", "1070", "1071", "1072", "1073", "1074", "1075"], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\pages\\AdminTemplateManagement.jsx", ["1076", "1077", "1078", "1079"], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\services\\responsableTemplateService.js", [], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\services\\baseTemplateService.js", [], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\pages\\BonLivraison.jsx", ["1080", "1081"], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\pages\\Livreurs.jsx", ["1082"], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\services\\bonLivraisonService.js", [], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\components\\LivreurForm.jsx", ["1083"], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\services\\livreurService.js", ["1084", "1085", "1086"], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\components\\BonLivraisonForm.jsx", ["1087", "1088"], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\pages\\Entreprise.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Gestion de facture et devis\\front-end\\src\\components\\BonLivraisonViewDialog.jsx", [], [], {"ruleId": "1089", "severity": 1, "message": "1090", "line": 25, "column": 8, "nodeType": "1091", "messageId": "1092", "endLine": 25, "endColumn": 18}, {"ruleId": "1089", "severity": 1, "message": "1093", "line": 37, "column": 8, "nodeType": "1091", "messageId": "1092", "endLine": 37, "endColumn": 30}, {"ruleId": "1089", "severity": 1, "message": "1094", "line": 38, "column": 8, "nodeType": "1091", "messageId": "1092", "endLine": 38, "endColumn": 24}, {"ruleId": "1089", "severity": 1, "message": "1095", "line": 18, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 18, "endColumn": 10}, {"ruleId": "1089", "severity": 1, "message": "1096", "line": 125, "column": 16, "nodeType": "1091", "messageId": "1092", "endLine": 125, "endColumn": 23}, {"ruleId": "1089", "severity": 1, "message": "1097", "line": 11, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 11, "endColumn": 17}, {"ruleId": "1089", "severity": 1, "message": "1098", "line": 13, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 13, "endColumn": 8}, {"ruleId": "1089", "severity": 1, "message": "1099", "line": 14, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 14, "endColumn": 6}, {"ruleId": "1089", "severity": 1, "message": "1100", "line": 15, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 15, "endColumn": 7}, {"ruleId": "1089", "severity": 1, "message": "1101", "line": 43, "column": 15, "nodeType": "1091", "messageId": "1092", "endLine": 43, "endColumn": 27}, {"ruleId": "1089", "severity": 1, "message": "1102", "line": 50, "column": 120, "nodeType": "1091", "messageId": "1092", "endLine": 50, "endColumn": 126}, {"ruleId": "1089", "severity": 1, "message": "1103", "line": 138, "column": 10, "nodeType": "1091", "messageId": "1092", "endLine": 138, "endColumn": 18}, {"ruleId": "1089", "severity": 1, "message": "1104", "line": 261, "column": 9, "nodeType": "1091", "messageId": "1092", "endLine": 261, "endColumn": 24}, {"ruleId": "1089", "severity": 1, "message": "1098", "line": 13, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 13, "endColumn": 8}, {"ruleId": "1089", "severity": 1, "message": "1105", "line": 26, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 26, "endColumn": 10}, {"ruleId": "1089", "severity": 1, "message": "1106", "line": 30, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 30, "endColumn": 13}, {"ruleId": "1089", "severity": 1, "message": "1107", "line": 32, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 32, "endColumn": 8}, {"ruleId": "1089", "severity": 1, "message": "1108", "line": 45, "column": 17, "nodeType": "1091", "messageId": "1092", "endLine": 45, "endColumn": 31}, {"ruleId": "1089", "severity": 1, "message": "1109", "line": 46, "column": 14, "nodeType": "1091", "messageId": "1092", "endLine": 46, "endColumn": 25}, {"ruleId": "1089", "severity": 1, "message": "1110", "line": 47, "column": 17, "nodeType": "1091", "messageId": "1092", "endLine": 47, "endColumn": 31}, {"ruleId": "1089", "severity": 1, "message": "1111", "line": 49, "column": 15, "nodeType": "1091", "messageId": "1092", "endLine": 49, "endColumn": 27}, {"ruleId": "1089", "severity": 1, "message": "1112", "line": 110, "column": 9, "nodeType": "1091", "messageId": "1092", "endLine": 110, "endColumn": 23}, {"ruleId": "1089", "severity": 1, "message": "1113", "line": 122, "column": 9, "nodeType": "1091", "messageId": "1092", "endLine": 122, "endColumn": 27}, {"ruleId": "1114", "severity": 1, "message": "1115", "line": 158, "column": 6, "nodeType": "1116", "endLine": 158, "endColumn": 56, "suggestions": "1117"}, {"ruleId": "1089", "severity": 1, "message": "1098", "line": 12, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 12, "endColumn": 8}, {"ruleId": "1089", "severity": 1, "message": "1100", "line": 22, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 22, "endColumn": 7}, {"ruleId": "1089", "severity": 1, "message": "1099", "line": 23, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 23, "endColumn": 6}, {"ruleId": "1089", "severity": 1, "message": "1118", "line": 25, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 25, "endColumn": 14}, {"ruleId": "1089", "severity": 1, "message": "1119", "line": 26, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 26, "endColumn": 13}, {"ruleId": "1089", "severity": 1, "message": "1120", "line": 27, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 27, "endColumn": 9}, {"ruleId": "1089", "severity": 1, "message": "1111", "line": 49, "column": 15, "nodeType": "1091", "messageId": "1092", "endLine": 49, "endColumn": 27}, {"ruleId": "1089", "severity": 1, "message": "1121", "line": 51, "column": 14, "nodeType": "1091", "messageId": "1092", "endLine": 51, "endColumn": 25}, {"ruleId": "1089", "severity": 1, "message": "1122", "line": 52, "column": 18, "nodeType": "1091", "messageId": "1092", "endLine": 52, "endColumn": 33}, {"ruleId": "1089", "severity": 1, "message": "1123", "line": 65, "column": 22, "nodeType": "1091", "messageId": "1092", "endLine": 65, "endColumn": 36}, {"ruleId": "1089", "severity": 1, "message": "1124", "line": 75, "column": 10, "nodeType": "1091", "messageId": "1092", "endLine": 75, "endColumn": 17}, {"ruleId": "1114", "severity": 1, "message": "1125", "line": 126, "column": 6, "nodeType": "1116", "endLine": 126, "endColumn": 8, "suggestions": "1126"}, {"ruleId": "1114", "severity": 1, "message": "1127", "line": 130, "column": 6, "nodeType": "1116", "endLine": 130, "endColumn": 89, "suggestions": "1128"}, {"ruleId": "1114", "severity": 1, "message": "1129", "line": 154, "column": 6, "nodeType": "1116", "endLine": 154, "endColumn": 26, "suggestions": "1130"}, {"ruleId": "1089", "severity": 1, "message": "1131", "line": 281, "column": 9, "nodeType": "1091", "messageId": "1092", "endLine": 281, "endColumn": 23}, {"ruleId": "1089", "severity": 1, "message": "1132", "line": 381, "column": 9, "nodeType": "1091", "messageId": "1092", "endLine": 381, "endColumn": 23}, {"ruleId": "1089", "severity": 1, "message": "1133", "line": 38, "column": 10, "nodeType": "1091", "messageId": "1092", "endLine": 38, "endColumn": 18}, {"ruleId": "1089", "severity": 1, "message": "1134", "line": 1, "column": 29, "nodeType": "1091", "messageId": "1092", "endLine": 1, "endColumn": 38}, {"ruleId": "1089", "severity": 1, "message": "1135", "line": 21, "column": 5, "nodeType": "1091", "messageId": "1092", "endLine": 21, "endColumn": 15}, {"ruleId": "1089", "severity": 1, "message": "1105", "line": 25, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 25, "endColumn": 10}, {"ruleId": "1089", "severity": 1, "message": "1106", "line": 29, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 29, "endColumn": 13}, {"ruleId": "1089", "severity": 1, "message": "1108", "line": 45, "column": 17, "nodeType": "1091", "messageId": "1092", "endLine": 45, "endColumn": 31}, {"ruleId": "1089", "severity": 1, "message": "1110", "line": 46, "column": 17, "nodeType": "1091", "messageId": "1092", "endLine": 46, "endColumn": 31}, {"ruleId": "1089", "severity": 1, "message": "1111", "line": 48, "column": 15, "nodeType": "1091", "messageId": "1092", "endLine": 48, "endColumn": 27}, {"ruleId": "1089", "severity": 1, "message": "1112", "line": 111, "column": 9, "nodeType": "1091", "messageId": "1092", "endLine": 111, "endColumn": 23}, {"ruleId": "1089", "severity": 1, "message": "1113", "line": 131, "column": 9, "nodeType": "1091", "messageId": "1092", "endLine": 131, "endColumn": 27}, {"ruleId": "1114", "severity": 1, "message": "1136", "line": 183, "column": 6, "nodeType": "1116", "endLine": 183, "endColumn": 53, "suggestions": "1137"}, {"ruleId": "1089", "severity": 1, "message": "1138", "line": 15, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 15, "endColumn": 9}, {"ruleId": "1089", "severity": 1, "message": "1105", "line": 19, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 19, "endColumn": 10}, {"ruleId": "1089", "severity": 1, "message": "1107", "line": 24, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 24, "endColumn": 8}, {"ruleId": "1089", "severity": 1, "message": "1097", "line": 25, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 25, "endColumn": 17}, {"ruleId": "1089", "severity": 1, "message": "1139", "line": 38, "column": 14, "nodeType": "1091", "messageId": "1092", "endLine": 38, "endColumn": 25}, {"ruleId": "1089", "severity": 1, "message": "1140", "line": 39, "column": 18, "nodeType": "1091", "messageId": "1092", "endLine": 39, "endColumn": 33}, {"ruleId": "1114", "severity": 1, "message": "1141", "line": 119, "column": 6, "nodeType": "1116", "endLine": 119, "endColumn": 46, "suggestions": "1142"}, {"ruleId": "1089", "severity": 1, "message": "1143", "line": 13, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 13, "endColumn": 6}, {"ruleId": "1089", "severity": 1, "message": "1123", "line": 21, "column": 10, "nodeType": "1091", "messageId": "1092", "endLine": 21, "endColumn": 24}, {"ruleId": "1089", "severity": 1, "message": "1105", "line": 11, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 11, "endColumn": 10}, {"ruleId": "1089", "severity": 1, "message": "1099", "line": 21, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 21, "endColumn": 6}, {"ruleId": "1089", "severity": 1, "message": "1100", "line": 22, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 22, "endColumn": 7}, {"ruleId": "1089", "severity": 1, "message": "1144", "line": 33, "column": 15, "nodeType": "1091", "messageId": "1092", "endLine": 33, "endColumn": 27}, {"ruleId": "1089", "severity": 1, "message": "1145", "line": 34, "column": 15, "nodeType": "1091", "messageId": "1092", "endLine": 34, "endColumn": 27}, {"ruleId": "1089", "severity": 1, "message": "1146", "line": 35, "column": 12, "nodeType": "1091", "messageId": "1092", "endLine": 35, "endColumn": 21}, {"ruleId": "1089", "severity": 1, "message": "1147", "line": 30, "column": 8, "nodeType": "1091", "messageId": "1092", "endLine": 30, "endColumn": 19}, {"ruleId": "1089", "severity": 1, "message": "1148", "line": 2, "column": 10, "nodeType": "1091", "messageId": "1092", "endLine": 2, "endColumn": 29}, {"ruleId": "1089", "severity": 1, "message": "1123", "line": 3, "column": 10, "nodeType": "1091", "messageId": "1092", "endLine": 3, "endColumn": 24}, {"ruleId": "1089", "severity": 1, "message": "1149", "line": 3, "column": 26, "nodeType": "1091", "messageId": "1092", "endLine": 3, "endColumn": 44}, {"ruleId": "1089", "severity": 1, "message": "1148", "line": 2, "column": 10, "nodeType": "1091", "messageId": "1092", "endLine": 2, "endColumn": 29}, {"ruleId": "1089", "severity": 1, "message": "1123", "line": 3, "column": 10, "nodeType": "1091", "messageId": "1092", "endLine": 3, "endColumn": 24}, {"ruleId": "1089", "severity": 1, "message": "1149", "line": 3, "column": 26, "nodeType": "1091", "messageId": "1092", "endLine": 3, "endColumn": 44}, {"ruleId": "1150", "severity": 1, "message": "1151", "line": 179, "column": 3, "nodeType": "1152", "messageId": "1153", "endLine": 179, "endColumn": 18}, {"ruleId": "1089", "severity": 1, "message": "1147", "line": 31, "column": 8, "nodeType": "1091", "messageId": "1092", "endLine": 31, "endColumn": 19}, {"ruleId": "1089", "severity": 1, "message": "1135", "line": 15, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 15, "endColumn": 13}, {"ruleId": "1089", "severity": 1, "message": "1119", "line": 10, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 10, "endColumn": 13}, {"ruleId": "1089", "severity": 1, "message": "1149", "line": 64, "column": 62, "nodeType": "1091", "messageId": "1092", "endLine": 64, "endColumn": 80}, {"ruleId": "1089", "severity": 1, "message": "1119", "line": 13, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 13, "endColumn": 13}, {"ruleId": "1089", "severity": 1, "message": "1121", "line": 49, "column": 14, "nodeType": "1091", "messageId": "1092", "endLine": 49, "endColumn": 25}, {"ruleId": "1089", "severity": 1, "message": "1154", "line": 55, "column": 11, "nodeType": "1091", "messageId": "1092", "endLine": 55, "endColumn": 19}, {"ruleId": "1089", "severity": 1, "message": "1149", "line": 65, "column": 48, "nodeType": "1091", "messageId": "1092", "endLine": 65, "endColumn": 66}, {"ruleId": "1089", "severity": 1, "message": "1155", "line": 501, "column": 9, "nodeType": "1091", "messageId": "1092", "endLine": 501, "endColumn": 15}, {"ruleId": "1089", "severity": 1, "message": "1156", "line": 564, "column": 9, "nodeType": "1091", "messageId": "1092", "endLine": 564, "endColumn": 28}, {"ruleId": "1089", "severity": 1, "message": "1149", "line": 4, "column": 48, "nodeType": "1091", "messageId": "1092", "endLine": 4, "endColumn": 66}, {"ruleId": "1089", "severity": 1, "message": "1157", "line": 11, "column": 54, "nodeType": "1091", "messageId": "1092", "endLine": 11, "endColumn": 67}, {"ruleId": "1114", "severity": 1, "message": "1158", "line": 37, "column": 23, "nodeType": "1091", "endLine": 37, "endColumn": 30}, {"ruleId": "1089", "severity": 1, "message": "1159", "line": 27, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 27, "endColumn": 9}, {"ruleId": "1160", "severity": 1, "message": "1161", "line": 139, "column": 11, "nodeType": "1162", "messageId": "1163", "endLine": 139, "endColumn": 59}, {"ruleId": "1160", "severity": 1, "message": "1161", "line": 239, "column": 9, "nodeType": "1162", "messageId": "1163", "endLine": 242, "endColumn": 11}, {"ruleId": "1089", "severity": 1, "message": "1164", "line": 2, "column": 18, "nodeType": "1091", "messageId": "1092", "endLine": 2, "endColumn": 28}, {"ruleId": "1089", "severity": 1, "message": "1165", "line": 6, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 6, "endColumn": 12}, {"ruleId": "1089", "severity": 1, "message": "1166", "line": 7, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 7, "endColumn": 7}, {"ruleId": "1089", "severity": 1, "message": "1105", "line": 9, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 9, "endColumn": 10}, {"ruleId": "1089", "severity": 1, "message": "1167", "line": 25, "column": 8, "nodeType": "1091", "messageId": "1092", "endLine": 25, "endColumn": 18}, {"ruleId": "1089", "severity": 1, "message": "1168", "line": 26, "column": 8, "nodeType": "1091", "messageId": "1092", "endLine": 26, "endColumn": 20}, {"ruleId": "1089", "severity": 1, "message": "1105", "line": 9, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 9, "endColumn": 10}, {"ruleId": "1089", "severity": 1, "message": "1169", "line": 16, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 16, "endColumn": 13}, {"ruleId": "1089", "severity": 1, "message": "1170", "line": 17, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 17, "endColumn": 19}, {"ruleId": "1089", "severity": 1, "message": "1171", "line": 18, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 18, "endColumn": 8}, {"ruleId": "1089", "severity": 1, "message": "1098", "line": 20, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 20, "endColumn": 8}, {"ruleId": "1089", "severity": 1, "message": "1166", "line": 26, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 26, "endColumn": 7}, {"ruleId": "1089", "severity": 1, "message": "1172", "line": 28, "column": 18, "nodeType": "1091", "messageId": "1092", "endLine": 28, "endColumn": 33}, {"ruleId": "1089", "severity": 1, "message": "1173", "line": 138, "column": 9, "nodeType": "1091", "messageId": "1092", "endLine": 138, "endColumn": 25}, {"ruleId": "1089", "severity": 1, "message": "1098", "line": 12, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 12, "endColumn": 8}, {"ruleId": "1089", "severity": 1, "message": "1174", "line": 176, "column": 11, "nodeType": "1091", "messageId": "1092", "endLine": 176, "endColumn": 26}, {"ruleId": "1089", "severity": 1, "message": "1175", "line": 21, "column": 8, "nodeType": "1091", "messageId": "1092", "endLine": 21, "endColumn": 29}, {"ruleId": "1089", "severity": 1, "message": "1176", "line": 26, "column": 8, "nodeType": "1091", "messageId": "1092", "endLine": 26, "endColumn": 21}, {"ruleId": "1089", "severity": 1, "message": "1121", "line": 28, "column": 8, "nodeType": "1091", "messageId": "1092", "endLine": 28, "endColumn": 19}, {"ruleId": "1089", "severity": 1, "message": "1122", "line": 29, "column": 8, "nodeType": "1091", "messageId": "1092", "endLine": 29, "endColumn": 23}, {"ruleId": "1089", "severity": 1, "message": "1177", "line": 18, "column": 8, "nodeType": "1091", "messageId": "1092", "endLine": 18, "endColumn": 21}, {"ruleId": "1089", "severity": 1, "message": "1178", "line": 31, "column": 8, "nodeType": "1091", "messageId": "1092", "endLine": 31, "endColumn": 18}, {"ruleId": "1089", "severity": 1, "message": "1096", "line": 206, "column": 16, "nodeType": "1091", "messageId": "1092", "endLine": 206, "endColumn": 23}, {"ruleId": "1089", "severity": 1, "message": "1179", "line": 218, "column": 9, "nodeType": "1091", "messageId": "1092", "endLine": 218, "endColumn": 17}, {"ruleId": "1089", "severity": 1, "message": "1180", "line": 226, "column": 9, "nodeType": "1091", "messageId": "1092", "endLine": 226, "endColumn": 22}, {"ruleId": "1089", "severity": 1, "message": "1181", "line": 24, "column": 10, "nodeType": "1091", "messageId": "1092", "endLine": 24, "endColumn": 17}, {"ruleId": "1089", "severity": 1, "message": "1182", "line": 88, "column": 9, "nodeType": "1091", "messageId": "1092", "endLine": 88, "endColumn": 14}, {"ruleId": "1089", "severity": 1, "message": "1183", "line": 92, "column": 10, "nodeType": "1091", "messageId": "1092", "endLine": 92, "endColumn": 29}, {"ruleId": "1089", "severity": 1, "message": "1184", "line": 98, "column": 9, "nodeType": "1091", "messageId": "1092", "endLine": 98, "endColumn": 30}, {"ruleId": "1089", "severity": 1, "message": "1185", "line": 126, "column": 9, "nodeType": "1091", "messageId": "1092", "endLine": 126, "endColumn": 25}, {"ruleId": "1089", "severity": 1, "message": "1186", "line": 131, "column": 9, "nodeType": "1091", "messageId": "1092", "endLine": 131, "endColumn": 23}, {"ruleId": "1089", "severity": 1, "message": "1106", "line": 9, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 9, "endColumn": 13}, {"ruleId": "1089", "severity": 1, "message": "1105", "line": 10, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 10, "endColumn": 10}, {"ruleId": "1089", "severity": 1, "message": "1098", "line": 17, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 17, "endColumn": 8}, {"ruleId": "1089", "severity": 1, "message": "1095", "line": 35, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 35, "endColumn": 10}, {"ruleId": "1089", "severity": 1, "message": "1110", "line": 42, "column": 17, "nodeType": "1091", "messageId": "1092", "endLine": 42, "endColumn": 31}, {"ruleId": "1089", "severity": 1, "message": "1187", "line": 43, "column": 14, "nodeType": "1091", "messageId": "1092", "endLine": 43, "endColumn": 25}, {"ruleId": "1089", "severity": 1, "message": "1188", "line": 54, "column": 7, "nodeType": "1091", "messageId": "1092", "endLine": 54, "endColumn": 16}, {"ruleId": "1089", "severity": 1, "message": "1182", "line": 94, "column": 9, "nodeType": "1091", "messageId": "1092", "endLine": 94, "endColumn": 14}, {"ruleId": "1089", "severity": 1, "message": "1189", "line": 96, "column": 11, "nodeType": "1091", "messageId": "1092", "endLine": 96, "endColumn": 22}, {"ruleId": "1089", "severity": 1, "message": "1190", "line": 104, "column": 10, "nodeType": "1091", "messageId": "1092", "endLine": 104, "endColumn": 22}, {"ruleId": "1089", "severity": 1, "message": "1112", "line": 263, "column": 9, "nodeType": "1091", "messageId": "1092", "endLine": 263, "endColumn": 23}, {"ruleId": "1089", "severity": 1, "message": "1159", "line": 18, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 18, "endColumn": 9}, {"ruleId": "1089", "severity": 1, "message": "1170", "line": 19, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 19, "endColumn": 19}, {"ruleId": "1089", "severity": 1, "message": "1098", "line": 25, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 25, "endColumn": 8}, {"ruleId": "1089", "severity": 1, "message": "1188", "line": 42, "column": 7, "nodeType": "1091", "messageId": "1092", "endLine": 42, "endColumn": 16}, {"ruleId": "1089", "severity": 1, "message": "1189", "line": 106, "column": 11, "nodeType": "1091", "messageId": "1092", "endLine": 106, "endColumn": 22}, {"ruleId": "1089", "severity": 1, "message": "1106", "line": 9, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 9, "endColumn": 13}, {"ruleId": "1089", "severity": 1, "message": "1105", "line": 10, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 10, "endColumn": 10}, {"ruleId": "1089", "severity": 1, "message": "1098", "line": 17, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 17, "endColumn": 8}, {"ruleId": "1089", "severity": 1, "message": "1191", "line": 19, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 19, "endColumn": 7}, {"ruleId": "1089", "severity": 1, "message": "1192", "line": 30, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 30, "endColumn": 7}, {"ruleId": "1089", "severity": 1, "message": "1193", "line": 31, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 31, "endColumn": 11}, {"ruleId": "1089", "severity": 1, "message": "1194", "line": 32, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 32, "endColumn": 15}, {"ruleId": "1089", "severity": 1, "message": "1110", "line": 42, "column": 17, "nodeType": "1091", "messageId": "1092", "endLine": 42, "endColumn": 31}, {"ruleId": "1089", "severity": 1, "message": "1187", "line": 43, "column": 14, "nodeType": "1091", "messageId": "1092", "endLine": 43, "endColumn": 25}, {"ruleId": "1089", "severity": 1, "message": "1111", "line": 44, "column": 15, "nodeType": "1091", "messageId": "1092", "endLine": 44, "endColumn": 27}, {"ruleId": "1089", "severity": 1, "message": "1195", "line": 48, "column": 20, "nodeType": "1091", "messageId": "1092", "endLine": 48, "endColumn": 32}, {"ruleId": "1089", "severity": 1, "message": "1108", "line": 49, "column": 17, "nodeType": "1091", "messageId": "1092", "endLine": 49, "endColumn": 31}, {"ruleId": "1089", "severity": 1, "message": "1196", "line": 57, "column": 7, "nodeType": "1091", "messageId": "1092", "endLine": 57, "endColumn": 19}, {"ruleId": "1089", "severity": 1, "message": "1189", "line": 107, "column": 11, "nodeType": "1091", "messageId": "1092", "endLine": 107, "endColumn": 22}, {"ruleId": "1089", "severity": 1, "message": "1197", "line": 115, "column": 10, "nodeType": "1091", "messageId": "1092", "endLine": 115, "endColumn": 25}, {"ruleId": "1089", "severity": 1, "message": "1198", "line": 121, "column": 10, "nodeType": "1091", "messageId": "1092", "endLine": 121, "endColumn": 18}, {"ruleId": "1089", "severity": 1, "message": "1199", "line": 160, "column": 9, "nodeType": "1091", "messageId": "1092", "endLine": 160, "endColumn": 23}, {"ruleId": "1089", "severity": 1, "message": "1200", "line": 166, "column": 9, "nodeType": "1091", "messageId": "1092", "endLine": 166, "endColumn": 24}, {"ruleId": "1089", "severity": 1, "message": "1201", "line": 172, "column": 9, "nodeType": "1091", "messageId": "1092", "endLine": 172, "endColumn": 26}, {"ruleId": "1089", "severity": 1, "message": "1106", "line": 9, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 9, "endColumn": 13}, {"ruleId": "1089", "severity": 1, "message": "1098", "line": 17, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 17, "endColumn": 8}, {"ruleId": "1089", "severity": 1, "message": "1191", "line": 19, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 19, "endColumn": 7}, {"ruleId": "1089", "severity": 1, "message": "1192", "line": 30, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 30, "endColumn": 7}, {"ruleId": "1089", "severity": 1, "message": "1193", "line": 31, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 31, "endColumn": 11}, {"ruleId": "1089", "severity": 1, "message": "1194", "line": 32, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 32, "endColumn": 15}, {"ruleId": "1089", "severity": 1, "message": "1110", "line": 41, "column": 17, "nodeType": "1091", "messageId": "1092", "endLine": 41, "endColumn": 31}, {"ruleId": "1089", "severity": 1, "message": "1187", "line": 42, "column": 14, "nodeType": "1091", "messageId": "1092", "endLine": 42, "endColumn": 25}, {"ruleId": "1089", "severity": 1, "message": "1195", "line": 47, "column": 20, "nodeType": "1091", "messageId": "1092", "endLine": 47, "endColumn": 32}, {"ruleId": "1089", "severity": 1, "message": "1108", "line": 48, "column": 17, "nodeType": "1091", "messageId": "1092", "endLine": 48, "endColumn": 31}, {"ruleId": "1089", "severity": 1, "message": "1202", "line": 55, "column": 7, "nodeType": "1091", "messageId": "1092", "endLine": 55, "endColumn": 22}, {"ruleId": "1089", "severity": 1, "message": "1189", "line": 113, "column": 11, "nodeType": "1091", "messageId": "1092", "endLine": 113, "endColumn": 22}, {"ruleId": "1089", "severity": 1, "message": "1203", "line": 120, "column": 10, "nodeType": "1091", "messageId": "1092", "endLine": 120, "endColumn": 28}, {"ruleId": "1089", "severity": 1, "message": "1198", "line": 126, "column": 10, "nodeType": "1091", "messageId": "1092", "endLine": 126, "endColumn": 18}, {"ruleId": "1089", "severity": 1, "message": "1199", "line": 167, "column": 9, "nodeType": "1091", "messageId": "1092", "endLine": 167, "endColumn": 23}, {"ruleId": "1089", "severity": 1, "message": "1200", "line": 173, "column": 9, "nodeType": "1091", "messageId": "1092", "endLine": 173, "endColumn": 24}, {"ruleId": "1089", "severity": 1, "message": "1201", "line": 179, "column": 9, "nodeType": "1091", "messageId": "1092", "endLine": 179, "endColumn": 26}, {"ruleId": "1089", "severity": 1, "message": "1170", "line": 17, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 17, "endColumn": 19}, {"ruleId": "1089", "severity": 1, "message": "1159", "line": 18, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 18, "endColumn": 9}, {"ruleId": "1089", "severity": 1, "message": "1135", "line": 25, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 25, "endColumn": 13}, {"ruleId": "1089", "severity": 1, "message": "1204", "line": 33, "column": 12, "nodeType": "1091", "messageId": "1092", "endLine": 33, "endColumn": 21}, {"ruleId": "1089", "severity": 1, "message": "1144", "line": 36, "column": 15, "nodeType": "1091", "messageId": "1092", "endLine": 36, "endColumn": 27}, {"ruleId": "1089", "severity": 1, "message": "1182", "line": 45, "column": 9, "nodeType": "1091", "messageId": "1092", "endLine": 45, "endColumn": 14}, {"ruleId": "1089", "severity": 1, "message": "1205", "line": 46, "column": 9, "nodeType": "1091", "messageId": "1092", "endLine": 46, "endColumn": 17}, {"ruleId": "1089", "severity": 1, "message": "1189", "line": 47, "column": 11, "nodeType": "1091", "messageId": "1092", "endLine": 47, "endColumn": 22}, {"ruleId": "1089", "severity": 1, "message": "1206", "line": 48, "column": 21, "nodeType": "1091", "messageId": "1092", "endLine": 48, "endColumn": 35}, {"ruleId": "1114", "severity": 1, "message": "1207", "line": 127, "column": 6, "nodeType": "1116", "endLine": 127, "endColumn": 8, "suggestions": "1208"}, {"ruleId": "1089", "severity": 1, "message": "1209", "line": 149, "column": 9, "nodeType": "1091", "messageId": "1092", "endLine": 149, "endColumn": 27}, {"ruleId": "1160", "severity": 1, "message": "1161", "line": 245, "column": 7, "nodeType": "1162", "messageId": "1163", "endLine": 248, "endColumn": 9}, {"ruleId": "1160", "severity": 1, "message": "1161", "line": 283, "column": 7, "nodeType": "1162", "messageId": "1163", "endLine": 286, "endColumn": 9}, {"ruleId": "1210", "severity": 1, "message": "1211", "line": 318, "column": 1, "nodeType": "1212", "endLine": 327, "endColumn": 3}, {"ruleId": "1114", "severity": 1, "message": "1207", "line": 45, "column": 6, "nodeType": "1116", "endLine": 45, "endColumn": 8, "suggestions": "1213"}, {"ruleId": "1150", "severity": 1, "message": "1214", "line": 274, "column": 3, "nodeType": "1152", "messageId": "1153", "endLine": 274, "endColumn": 17}, {"ruleId": "1089", "severity": 1, "message": "1215", "line": 18, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 18, "endColumn": 14}, {"ruleId": "1089", "severity": 1, "message": "1107", "line": 36, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 36, "endColumn": 8}, {"ruleId": "1089", "severity": 1, "message": "1216", "line": 47, "column": 17, "nodeType": "1091", "messageId": "1092", "endLine": 47, "endColumn": 25}, {"ruleId": "1089", "severity": 1, "message": "1217", "line": 54, "column": 12, "nodeType": "1091", "messageId": "1092", "endLine": 54, "endColumn": 21}, {"ruleId": "1089", "severity": 1, "message": "1204", "line": 55, "column": 12, "nodeType": "1091", "messageId": "1092", "endLine": 55, "endColumn": 21}, {"ruleId": "1089", "severity": 1, "message": "1218", "line": 60, "column": 12, "nodeType": "1091", "messageId": "1092", "endLine": 60, "endColumn": 21}, {"ruleId": "1089", "severity": 1, "message": "1219", "line": 61, "column": 18, "nodeType": "1091", "messageId": "1092", "endLine": 61, "endColumn": 33}, {"ruleId": "1089", "severity": 1, "message": "1220", "line": 62, "column": 20, "nodeType": "1091", "messageId": "1092", "endLine": 62, "endColumn": 37}, {"ruleId": "1089", "severity": 1, "message": "1221", "line": 152, "column": 9, "nodeType": "1091", "messageId": "1092", "endLine": 152, "endColumn": 17}, {"ruleId": "1089", "severity": 1, "message": "1222", "line": 153, "column": 9, "nodeType": "1091", "messageId": "1092", "endLine": 153, "endColumn": 17}, {"ruleId": "1089", "severity": 1, "message": "1223", "line": 155, "column": 10, "nodeType": "1091", "messageId": "1092", "endLine": 155, "endColumn": 17}, {"ruleId": "1114", "severity": 1, "message": "1115", "line": 289, "column": 6, "nodeType": "1116", "endLine": 289, "endColumn": 54, "suggestions": "1224"}, {"ruleId": "1089", "severity": 1, "message": "1182", "line": 25, "column": 9, "nodeType": "1091", "messageId": "1092", "endLine": 25, "endColumn": 14}, {"ruleId": "1089", "severity": 1, "message": "1105", "line": 18, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 18, "endColumn": 10}, {"ruleId": "1089", "severity": 1, "message": "1099", "line": 27, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 27, "endColumn": 6}, {"ruleId": "1089", "severity": 1, "message": "1100", "line": 28, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 28, "endColumn": 7}, {"ruleId": "1089", "severity": 1, "message": "1225", "line": 32, "column": 19, "nodeType": "1091", "messageId": "1092", "endLine": 32, "endColumn": 35}, {"ruleId": "1089", "severity": 1, "message": "1226", "line": 61, "column": 10, "nodeType": "1091", "messageId": "1092", "endLine": 61, "endColumn": 19}, {"ruleId": "1089", "severity": 1, "message": "1227", "line": 74, "column": 13, "nodeType": "1091", "messageId": "1092", "endLine": 74, "endColumn": 18}, {"ruleId": "1089", "severity": 1, "message": "1104", "line": 166, "column": 9, "nodeType": "1091", "messageId": "1092", "endLine": 166, "endColumn": 24}, {"ruleId": "1089", "severity": 1, "message": "1228", "line": 14, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 14, "endColumn": 9}, {"ruleId": "1089", "severity": 1, "message": "1138", "line": 17, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 17, "endColumn": 9}, {"ruleId": "1089", "severity": 1, "message": "1215", "line": 18, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 18, "endColumn": 14}, {"ruleId": "1089", "severity": 1, "message": "1229", "line": 19, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 19, "endColumn": 16}, {"ruleId": "1089", "severity": 1, "message": "1230", "line": 20, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 20, "endColumn": 16}, {"ruleId": "1089", "severity": 1, "message": "1231", "line": 26, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 26, "endColumn": 8}, {"ruleId": "1089", "severity": 1, "message": "1232", "line": 41, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 41, "endColumn": 15}, {"ruleId": "1089", "severity": 1, "message": "1194", "line": 42, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 42, "endColumn": 15}, {"ruleId": "1089", "severity": 1, "message": "1233", "line": 43, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 43, "endColumn": 26}, {"ruleId": "1089", "severity": 1, "message": "1234", "line": 50, "column": 17, "nodeType": "1091", "messageId": "1092", "endLine": 50, "endColumn": 27}, {"ruleId": "1089", "severity": 1, "message": "1217", "line": 52, "column": 12, "nodeType": "1091", "messageId": "1092", "endLine": 52, "endColumn": 21}, {"ruleId": "1089", "severity": 1, "message": "1204", "line": 53, "column": 12, "nodeType": "1091", "messageId": "1092", "endLine": 53, "endColumn": 21}, {"ruleId": "1089", "severity": 1, "message": "1216", "line": 56, "column": 17, "nodeType": "1091", "messageId": "1092", "endLine": 56, "endColumn": 25}, {"ruleId": "1089", "severity": 1, "message": "1189", "line": 73, "column": 11, "nodeType": "1091", "messageId": "1092", "endLine": 73, "endColumn": 22}, {"ruleId": "1089", "severity": 1, "message": "1235", "line": 84, "column": 10, "nodeType": "1091", "messageId": "1092", "endLine": 84, "endColumn": 15}, {"ruleId": "1114", "severity": 1, "message": "1236", "line": 172, "column": 6, "nodeType": "1116", "endLine": 172, "endColumn": 65, "suggestions": "1237"}, {"ruleId": "1089", "severity": 1, "message": "1238", "line": 258, "column": 13, "nodeType": "1091", "messageId": "1092", "endLine": 258, "endColumn": 20}, {"ruleId": "1089", "severity": 1, "message": "1239", "line": 13, "column": 8, "nodeType": "1091", "messageId": "1092", "endLine": 13, "endColumn": 22}, {"ruleId": "1089", "severity": 1, "message": "1165", "line": 6, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 6, "endColumn": 12}, {"ruleId": "1089", "severity": 1, "message": "1166", "line": 7, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 7, "endColumn": 7}, {"ruleId": "1089", "severity": 1, "message": "1191", "line": 17, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 17, "endColumn": 7}, {"ruleId": "1089", "severity": 1, "message": "1240", "line": 23, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 23, "endColumn": 8}, {"ruleId": "1089", "severity": 1, "message": "1095", "line": 24, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 24, "endColumn": 10}, {"ruleId": "1089", "severity": 1, "message": "1135", "line": 25, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 25, "endColumn": 13}, {"ruleId": "1089", "severity": 1, "message": "1241", "line": 28, "column": 16, "nodeType": "1091", "messageId": "1092", "endLine": 28, "endColumn": 29}, {"ruleId": "1089", "severity": 1, "message": "1242", "line": 29, "column": 17, "nodeType": "1091", "messageId": "1092", "endLine": 29, "endColumn": 31}, {"ruleId": "1089", "severity": 1, "message": "1167", "line": 30, "column": 13, "nodeType": "1091", "messageId": "1092", "endLine": 30, "endColumn": 23}, {"ruleId": "1089", "severity": 1, "message": "1168", "line": 31, "column": 15, "nodeType": "1091", "messageId": "1092", "endLine": 31, "endColumn": 27}, {"ruleId": "1089", "severity": 1, "message": "1175", "line": 32, "column": 24, "nodeType": "1091", "messageId": "1092", "endLine": 32, "endColumn": 45}, {"ruleId": "1089", "severity": 1, "message": "1111", "line": 33, "column": 15, "nodeType": "1091", "messageId": "1092", "endLine": 33, "endColumn": 27}, {"ruleId": "1089", "severity": 1, "message": "1118", "line": 14, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 14, "endColumn": 14}, {"ruleId": "1089", "severity": 1, "message": "1119", "line": 15, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 15, "endColumn": 13}, {"ruleId": "1089", "severity": 1, "message": "1120", "line": 16, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 16, "endColumn": 9}, {"ruleId": "1089", "severity": 1, "message": "1193", "line": 17, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 17, "endColumn": 11}, {"ruleId": "1089", "severity": 1, "message": "1243", "line": 20, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 20, "endColumn": 19}, {"ruleId": "1089", "severity": 1, "message": "1098", "line": 24, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 24, "endColumn": 8}, {"ruleId": "1089", "severity": 1, "message": "1244", "line": 32, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 32, "endColumn": 7}, {"ruleId": "1089", "severity": 1, "message": "1245", "line": 33, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 33, "endColumn": 7}, {"ruleId": "1089", "severity": 1, "message": "1102", "line": 45, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 45, "endColumn": 9}, {"ruleId": "1089", "severity": 1, "message": "1246", "line": 51, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 51, "endColumn": 13}, {"ruleId": "1089", "severity": 1, "message": "1247", "line": 52, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 52, "endColumn": 8}, {"ruleId": "1089", "severity": 1, "message": "1248", "line": 53, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 53, "endColumn": 12}, {"ruleId": "1089", "severity": 1, "message": "1249", "line": 54, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 54, "endColumn": 17}, {"ruleId": "1089", "severity": 1, "message": "1250", "line": 55, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 55, "endColumn": 18}, {"ruleId": "1089", "severity": 1, "message": "1234", "line": 62, "column": 16, "nodeType": "1091", "messageId": "1092", "endLine": 62, "endColumn": 26}, {"ruleId": "1089", "severity": 1, "message": "1195", "line": 64, "column": 20, "nodeType": "1091", "messageId": "1092", "endLine": 64, "endColumn": 32}, {"ruleId": "1089", "severity": 1, "message": "1251", "line": 68, "column": 13, "nodeType": "1091", "messageId": "1092", "endLine": 68, "endColumn": 23}, {"ruleId": "1089", "severity": 1, "message": "1252", "line": 69, "column": 17, "nodeType": "1091", "messageId": "1092", "endLine": 69, "endColumn": 31}, {"ruleId": "1089", "severity": 1, "message": "1253", "line": 74, "column": 11, "nodeType": "1091", "messageId": "1092", "endLine": 74, "endColumn": 19}, {"ruleId": "1089", "severity": 1, "message": "1254", "line": 75, "column": 11, "nodeType": "1091", "messageId": "1092", "endLine": 75, "endColumn": 19}, {"ruleId": "1089", "severity": 1, "message": "1122", "line": 77, "column": 18, "nodeType": "1091", "messageId": "1092", "endLine": 77, "endColumn": 33}, {"ruleId": "1089", "severity": 1, "message": "1223", "line": 131, "column": 10, "nodeType": "1091", "messageId": "1092", "endLine": 131, "endColumn": 17}, {"ruleId": "1089", "severity": 1, "message": "1255", "line": 144, "column": 10, "nodeType": "1091", "messageId": "1092", "endLine": 144, "endColumn": 19}, {"ruleId": "1089", "severity": 1, "message": "1256", "line": 223, "column": 9, "nodeType": "1091", "messageId": "1092", "endLine": 223, "endColumn": 27}, {"ruleId": "1089", "severity": 1, "message": "1257", "line": 233, "column": 9, "nodeType": "1091", "messageId": "1092", "endLine": 233, "endColumn": 30}, {"ruleId": "1089", "severity": 1, "message": "1258", "line": 238, "column": 9, "nodeType": "1091", "messageId": "1092", "endLine": 238, "endColumn": 21}, {"ruleId": "1089", "severity": 1, "message": "1259", "line": 244, "column": 9, "nodeType": "1091", "messageId": "1092", "endLine": 244, "endColumn": 30}, {"ruleId": "1089", "severity": 1, "message": "1135", "line": 16, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 16, "endColumn": 13}, {"ruleId": "1089", "severity": 1, "message": "1138", "line": 23, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 23, "endColumn": 9}, {"ruleId": "1089", "severity": 1, "message": "1230", "line": 24, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 24, "endColumn": 16}, {"ruleId": "1089", "severity": 1, "message": "1229", "line": 25, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 25, "endColumn": 16}, {"ruleId": "1089", "severity": 1, "message": "1215", "line": 26, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 26, "endColumn": 14}, {"ruleId": "1089", "severity": 1, "message": "1193", "line": 27, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 27, "endColumn": 11}, {"ruleId": "1089", "severity": 1, "message": "1118", "line": 28, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 28, "endColumn": 14}, {"ruleId": "1089", "severity": 1, "message": "1120", "line": 29, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 29, "endColumn": 9}, {"ruleId": "1089", "severity": 1, "message": "1260", "line": 52, "column": 19, "nodeType": "1091", "messageId": "1092", "endLine": 52, "endColumn": 35}, {"ruleId": "1089", "severity": 1, "message": "1176", "line": 53, "column": 16, "nodeType": "1091", "messageId": "1092", "endLine": 53, "endColumn": 29}, {"ruleId": "1089", "severity": 1, "message": "1219", "line": 54, "column": 18, "nodeType": "1091", "messageId": "1092", "endLine": 54, "endColumn": 33}, {"ruleId": "1089", "severity": 1, "message": "1220", "line": 55, "column": 20, "nodeType": "1091", "messageId": "1092", "endLine": 55, "endColumn": 37}, {"ruleId": "1089", "severity": 1, "message": "1261", "line": 57, "column": 12, "nodeType": "1091", "messageId": "1092", "endLine": 57, "endColumn": 21}, {"ruleId": "1089", "severity": 1, "message": "1262", "line": 64, "column": 10, "nodeType": "1091", "messageId": "1092", "endLine": 64, "endColumn": 24}, {"ruleId": "1089", "severity": 1, "message": "1263", "line": 65, "column": 10, "nodeType": "1091", "messageId": "1092", "endLine": 65, "endColumn": 30}, {"ruleId": "1089", "severity": 1, "message": "1264", "line": 65, "column": 32, "nodeType": "1091", "messageId": "1092", "endLine": 65, "endColumn": 42}, {"ruleId": "1089", "severity": 1, "message": "1265", "line": 66, "column": 10, "nodeType": "1091", "messageId": "1092", "endLine": 66, "endColumn": 12}, {"ruleId": "1089", "severity": 1, "message": "1223", "line": 116, "column": 10, "nodeType": "1091", "messageId": "1092", "endLine": 116, "endColumn": 17}, {"ruleId": "1089", "severity": 1, "message": "1235", "line": 119, "column": 10, "nodeType": "1091", "messageId": "1092", "endLine": 119, "endColumn": 15}, {"ruleId": "1089", "severity": 1, "message": "1266", "line": 169, "column": 10, "nodeType": "1091", "messageId": "1092", "endLine": 169, "endColumn": 22}, {"ruleId": "1114", "severity": 1, "message": "1267", "line": 397, "column": 6, "nodeType": "1116", "endLine": 397, "endColumn": 8, "suggestions": "1268"}, {"ruleId": "1089", "severity": 1, "message": "1135", "line": 14, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 14, "endColumn": 13}, {"ruleId": "1089", "severity": 1, "message": "1095", "line": 15, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 15, "endColumn": 10}, {"ruleId": "1089", "severity": 1, "message": "1223", "line": 41, "column": 10, "nodeType": "1091", "messageId": "1092", "endLine": 41, "endColumn": 17}, {"ruleId": "1089", "severity": 1, "message": "1118", "line": 14, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 14, "endColumn": 14}, {"ruleId": "1089", "severity": 1, "message": "1120", "line": 15, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 15, "endColumn": 9}, {"ruleId": "1089", "severity": 1, "message": "1193", "line": 16, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 16, "endColumn": 11}, {"ruleId": "1089", "severity": 1, "message": "1099", "line": 17, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 17, "endColumn": 6}, {"ruleId": "1089", "severity": 1, "message": "1100", "line": 18, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 18, "endColumn": 7}, {"ruleId": "1089", "severity": 1, "message": "1135", "line": 22, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 22, "endColumn": 13}, {"ruleId": "1089", "severity": 1, "message": "1138", "line": 29, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 29, "endColumn": 9}, {"ruleId": "1089", "severity": 1, "message": "1230", "line": 30, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 30, "endColumn": 16}, {"ruleId": "1089", "severity": 1, "message": "1229", "line": 31, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 31, "endColumn": 16}, {"ruleId": "1089", "severity": 1, "message": "1215", "line": 32, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 32, "endColumn": 14}, {"ruleId": "1089", "severity": 1, "message": "1195", "line": 54, "column": 20, "nodeType": "1091", "messageId": "1092", "endLine": 54, "endColumn": 32}, {"ruleId": "1089", "severity": 1, "message": "1260", "line": 60, "column": 19, "nodeType": "1091", "messageId": "1092", "endLine": 60, "endColumn": 35}, {"ruleId": "1089", "severity": 1, "message": "1261", "line": 61, "column": 12, "nodeType": "1091", "messageId": "1092", "endLine": 61, "endColumn": 21}, {"ruleId": "1089", "severity": 1, "message": "1269", "line": 66, "column": 10, "nodeType": "1091", "messageId": "1092", "endLine": 66, "endColumn": 21}, {"ruleId": "1089", "severity": 1, "message": "1262", "line": 68, "column": 10, "nodeType": "1091", "messageId": "1092", "endLine": 68, "endColumn": 24}, {"ruleId": "1089", "severity": 1, "message": "1263", "line": 69, "column": 10, "nodeType": "1091", "messageId": "1092", "endLine": 69, "endColumn": 30}, {"ruleId": "1089", "severity": 1, "message": "1264", "line": 69, "column": 32, "nodeType": "1091", "messageId": "1092", "endLine": 69, "endColumn": 42}, {"ruleId": "1089", "severity": 1, "message": "1265", "line": 70, "column": 10, "nodeType": "1091", "messageId": "1092", "endLine": 70, "endColumn": 12}, {"ruleId": "1089", "severity": 1, "message": "1223", "line": 134, "column": 10, "nodeType": "1091", "messageId": "1092", "endLine": 134, "endColumn": 17}, {"ruleId": "1089", "severity": 1, "message": "1103", "line": 136, "column": 10, "nodeType": "1091", "messageId": "1092", "endLine": 136, "endColumn": 18}, {"ruleId": "1089", "severity": 1, "message": "1270", "line": 152, "column": 10, "nodeType": "1091", "messageId": "1092", "endLine": 152, "endColumn": 20}, {"ruleId": "1089", "severity": 1, "message": "1104", "line": 314, "column": 9, "nodeType": "1091", "messageId": "1092", "endLine": 314, "endColumn": 24}, {"ruleId": "1089", "severity": 1, "message": "1098", "line": 14, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 14, "endColumn": 8}, {"ruleId": "1089", "severity": 1, "message": "1243", "line": 28, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 28, "endColumn": 19}, {"ruleId": "1089", "severity": 1, "message": "1110", "line": 51, "column": 17, "nodeType": "1091", "messageId": "1092", "endLine": 51, "endColumn": 31}, {"ruleId": "1089", "severity": 1, "message": "1253", "line": 54, "column": 11, "nodeType": "1091", "messageId": "1092", "endLine": 54, "endColumn": 19}, {"ruleId": "1089", "severity": 1, "message": "1168", "line": 55, "column": 15, "nodeType": "1091", "messageId": "1092", "endLine": 55, "endColumn": 27}, {"ruleId": "1089", "severity": 1, "message": "1205", "line": 88, "column": 9, "nodeType": "1091", "messageId": "1092", "endLine": 88, "endColumn": 17}, {"ruleId": "1114", "severity": 1, "message": "1125", "line": 121, "column": 6, "nodeType": "1116", "endLine": 121, "endColumn": 8, "suggestions": "1271"}, {"ruleId": "1114", "severity": 1, "message": "1272", "line": 125, "column": 6, "nodeType": "1116", "endLine": 125, "endColumn": 42, "suggestions": "1273"}, {"ruleId": "1089", "severity": 1, "message": "1098", "line": 17, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 17, "endColumn": 8}, {"ruleId": "1089", "severity": 1, "message": "1165", "line": 24, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 24, "endColumn": 12}, {"ruleId": "1089", "severity": 1, "message": "1274", "line": 25, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 25, "endColumn": 14}, {"ruleId": "1089", "severity": 1, "message": "1109", "line": 34, "column": 14, "nodeType": "1091", "messageId": "1092", "endLine": 34, "endColumn": 25}, {"ruleId": "1089", "severity": 1, "message": "1275", "line": 39, "column": 15, "nodeType": "1091", "messageId": "1092", "endLine": 39, "endColumn": 27}, {"ruleId": "1089", "severity": 1, "message": "1167", "line": 41, "column": 13, "nodeType": "1091", "messageId": "1092", "endLine": 41, "endColumn": 23}, {"ruleId": "1089", "severity": 1, "message": "1276", "line": 46, "column": 16, "nodeType": "1091", "messageId": "1092", "endLine": 46, "endColumn": 29}, {"ruleId": "1089", "severity": 1, "message": "1277", "line": 47, "column": 15, "nodeType": "1091", "messageId": "1092", "endLine": 47, "endColumn": 27}, {"ruleId": "1089", "severity": 1, "message": "1260", "line": 48, "column": 19, "nodeType": "1091", "messageId": "1092", "endLine": 48, "endColumn": 35}, {"ruleId": "1089", "severity": 1, "message": "1278", "line": 51, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 51, "endColumn": 11}, {"ruleId": "1089", "severity": 1, "message": "1279", "line": 52, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 52, "endColumn": 6}, {"ruleId": "1089", "severity": 1, "message": "1280", "line": 53, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 53, "endColumn": 8}, {"ruleId": "1089", "severity": 1, "message": "1281", "line": 54, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 54, "endColumn": 8}, {"ruleId": "1089", "severity": 1, "message": "1282", "line": 55, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 55, "endColumn": 16}, {"ruleId": "1089", "severity": 1, "message": "1283", "line": 56, "column": 14, "nodeType": "1091", "messageId": "1092", "endLine": 56, "endColumn": 29}, {"ruleId": "1089", "severity": 1, "message": "1284", "line": 57, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 57, "endColumn": 22}, {"ruleId": "1089", "severity": 1, "message": "1285", "line": 58, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 58, "endColumn": 12}, {"ruleId": "1089", "severity": 1, "message": "1286", "line": 59, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 59, "endColumn": 7}, {"ruleId": "1089", "severity": 1, "message": "1287", "line": 60, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 60, "endColumn": 11}, {"ruleId": "1089", "severity": 1, "message": "1288", "line": 61, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 61, "endColumn": 6}, {"ruleId": "1089", "severity": 1, "message": "1289", "line": 62, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 62, "endColumn": 7}, {"ruleId": "1089", "severity": 1, "message": "1102", "line": 63, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 63, "endColumn": 9}, {"ruleId": "1089", "severity": 1, "message": "1290", "line": 90, "column": 7, "nodeType": "1091", "messageId": "1092", "endLine": 90, "endColumn": 13}, {"ruleId": "1089", "severity": 1, "message": "1291", "line": 100, "column": 10, "nodeType": "1091", "messageId": "1092", "endLine": 100, "endColumn": 19}, {"ruleId": "1089", "severity": 1, "message": "1292", "line": 103, "column": 10, "nodeType": "1091", "messageId": "1092", "endLine": 103, "endColumn": 19}, {"ruleId": "1089", "severity": 1, "message": "1293", "line": 105, "column": 10, "nodeType": "1091", "messageId": "1092", "endLine": 105, "endColumn": 21}, {"ruleId": "1114", "severity": 1, "message": "1294", "line": 492, "column": 6, "nodeType": "1116", "endLine": 492, "endColumn": 19, "suggestions": "1295"}, {"ruleId": "1089", "severity": 1, "message": "1257", "line": 629, "column": 9, "nodeType": "1091", "messageId": "1092", "endLine": 629, "endColumn": 30}, {"ruleId": "1089", "severity": 1, "message": "1105", "line": 11, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 11, "endColumn": 10}, {"ruleId": "1089", "severity": 1, "message": "1240", "line": 23, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 23, "endColumn": 8}, {"ruleId": "1089", "severity": 1, "message": "1167", "line": 39, "column": 13, "nodeType": "1091", "messageId": "1092", "endLine": 39, "endColumn": 23}, {"ruleId": "1089", "severity": 1, "message": "1168", "line": 40, "column": 15, "nodeType": "1091", "messageId": "1092", "endLine": 40, "endColumn": 27}, {"ruleId": "1089", "severity": 1, "message": "1296", "line": 8, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 8, "endColumn": 14}, {"ruleId": "1089", "severity": 1, "message": "1098", "line": 19, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 19, "endColumn": 8}, {"ruleId": "1089", "severity": 1, "message": "1138", "line": 22, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 22, "endColumn": 9}, {"ruleId": "1089", "severity": 1, "message": "1215", "line": 23, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 23, "endColumn": 14}, {"ruleId": "1089", "severity": 1, "message": "1229", "line": 24, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 24, "endColumn": 16}, {"ruleId": "1089", "severity": 1, "message": "1230", "line": 25, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 25, "endColumn": 16}, {"ruleId": "1089", "severity": 1, "message": "1110", "line": 38, "column": 17, "nodeType": "1091", "messageId": "1092", "endLine": 38, "endColumn": 31}, {"ruleId": "1114", "severity": 1, "message": "1297", "line": 162, "column": 6, "nodeType": "1116", "endLine": 162, "endColumn": 54, "suggestions": "1298"}, {"ruleId": "1089", "severity": 1, "message": "1296", "line": 8, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 8, "endColumn": 14}, {"ruleId": "1089", "severity": 1, "message": "1098", "line": 19, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 19, "endColumn": 8}, {"ruleId": "1089", "severity": 1, "message": "1110", "line": 39, "column": 17, "nodeType": "1091", "messageId": "1092", "endLine": 39, "endColumn": 31}, {"ruleId": "1114", "severity": 1, "message": "1297", "line": 196, "column": 6, "nodeType": "1116", "endLine": 196, "endColumn": 51, "suggestions": "1299"}, {"ruleId": "1089", "severity": 1, "message": "1105", "line": 11, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 11, "endColumn": 10}, {"ruleId": "1089", "severity": 1, "message": "1240", "line": 23, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 23, "endColumn": 8}, {"ruleId": "1089", "severity": 1, "message": "1167", "line": 39, "column": 13, "nodeType": "1091", "messageId": "1092", "endLine": 39, "endColumn": 23}, {"ruleId": "1089", "severity": 1, "message": "1168", "line": 40, "column": 15, "nodeType": "1091", "messageId": "1092", "endLine": 40, "endColumn": 27}, {"ruleId": "1089", "severity": 1, "message": "1105", "line": 10, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 10, "endColumn": 10}, {"ruleId": "1089", "severity": 1, "message": "1098", "line": 12, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 12, "endColumn": 8}, {"ruleId": "1089", "severity": 1, "message": "1300", "line": 16, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 16, "endColumn": 11}, {"ruleId": "1089", "severity": 1, "message": "1191", "line": 18, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 18, "endColumn": 7}, {"ruleId": "1089", "severity": 1, "message": "1095", "line": 22, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 22, "endColumn": 10}, {"ruleId": "1089", "severity": 1, "message": "1301", "line": 26, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 26, "endColumn": 20}, {"ruleId": "1089", "severity": 1, "message": "1302", "line": 36, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 36, "endColumn": 11}, {"ruleId": "1089", "severity": 1, "message": "1303", "line": 38, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 38, "endColumn": 7}, {"ruleId": "1089", "severity": 1, "message": "1304", "line": 71, "column": 10, "nodeType": "1091", "messageId": "1092", "endLine": 71, "endColumn": 24}, {"ruleId": "1089", "severity": 1, "message": "1305", "line": 71, "column": 26, "nodeType": "1091", "messageId": "1092", "endLine": 71, "endColumn": 43}, {"ruleId": "1089", "severity": 1, "message": "1193", "line": 26, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 26, "endColumn": 11}, {"ruleId": "1089", "severity": 1, "message": "1120", "line": 27, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 27, "endColumn": 9}, {"ruleId": "1089", "severity": 1, "message": "1118", "line": 28, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 28, "endColumn": 14}, {"ruleId": "1089", "severity": 1, "message": "1119", "line": 29, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 29, "endColumn": 13}, {"ruleId": "1089", "severity": 1, "message": "1105", "line": 31, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 31, "endColumn": 10}, {"ruleId": "1089", "severity": 1, "message": "1306", "line": 32, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 32, "endColumn": 7}, {"ruleId": "1089", "severity": 1, "message": "1307", "line": 33, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 33, "endColumn": 11}, {"ruleId": "1089", "severity": 1, "message": "1232", "line": 34, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 34, "endColumn": 15}, {"ruleId": "1089", "severity": 1, "message": "1194", "line": 35, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 35, "endColumn": 15}, {"ruleId": "1089", "severity": 1, "message": "1107", "line": 37, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 37, "endColumn": 8}, {"ruleId": "1089", "severity": 1, "message": "1097", "line": 38, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 38, "endColumn": 17}, {"ruleId": "1089", "severity": 1, "message": "1300", "line": 41, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 41, "endColumn": 11}, {"ruleId": "1089", "severity": 1, "message": "1108", "line": 47, "column": 17, "nodeType": "1091", "messageId": "1092", "endLine": 47, "endColumn": 31}, {"ruleId": "1089", "severity": 1, "message": "1217", "line": 50, "column": 12, "nodeType": "1091", "messageId": "1092", "endLine": 50, "endColumn": 21}, {"ruleId": "1089", "severity": 1, "message": "1216", "line": 53, "column": 17, "nodeType": "1091", "messageId": "1092", "endLine": 53, "endColumn": 25}, {"ruleId": "1089", "severity": 1, "message": "1204", "line": 58, "column": 12, "nodeType": "1091", "messageId": "1092", "endLine": 58, "endColumn": 21}, {"ruleId": "1089", "severity": 1, "message": "1139", "line": 62, "column": 14, "nodeType": "1091", "messageId": "1092", "endLine": 62, "endColumn": 25}, {"ruleId": "1089", "severity": 1, "message": "1218", "line": 63, "column": 12, "nodeType": "1091", "messageId": "1092", "endLine": 63, "endColumn": 21}, {"ruleId": "1089", "severity": 1, "message": "1219", "line": 64, "column": 18, "nodeType": "1091", "messageId": "1092", "endLine": 64, "endColumn": 33}, {"ruleId": "1089", "severity": 1, "message": "1220", "line": 65, "column": 20, "nodeType": "1091", "messageId": "1092", "endLine": 65, "endColumn": 37}, {"ruleId": "1089", "severity": 1, "message": "1239", "line": 69, "column": 8, "nodeType": "1091", "messageId": "1092", "endLine": 69, "endColumn": 22}, {"ruleId": "1089", "severity": 1, "message": "1308", "line": 70, "column": 38, "nodeType": "1091", "messageId": "1092", "endLine": 70, "endColumn": 50}, {"ruleId": "1089", "severity": 1, "message": "1112", "line": 134, "column": 7, "nodeType": "1091", "messageId": "1092", "endLine": 134, "endColumn": 21}, {"ruleId": "1089", "severity": 1, "message": "1309", "line": 149, "column": 7, "nodeType": "1091", "messageId": "1092", "endLine": 149, "endColumn": 20}, {"ruleId": "1089", "severity": 1, "message": "1205", "line": 192, "column": 9, "nodeType": "1091", "messageId": "1092", "endLine": 192, "endColumn": 17}, {"ruleId": "1089", "severity": 1, "message": "1221", "line": 194, "column": 9, "nodeType": "1091", "messageId": "1092", "endLine": 194, "endColumn": 17}, {"ruleId": "1089", "severity": 1, "message": "1222", "line": 195, "column": 9, "nodeType": "1091", "messageId": "1092", "endLine": 195, "endColumn": 17}, {"ruleId": "1089", "severity": 1, "message": "1291", "line": 198, "column": 10, "nodeType": "1091", "messageId": "1092", "endLine": 198, "endColumn": 19}, {"ruleId": "1114", "severity": 1, "message": "1310", "line": 316, "column": 6, "nodeType": "1116", "endLine": 316, "endColumn": 47, "suggestions": "1311"}, {"ruleId": "1114", "severity": 1, "message": "1312", "line": 333, "column": 6, "nodeType": "1116", "endLine": 333, "endColumn": 18, "suggestions": "1313"}, {"ruleId": "1089", "severity": 1, "message": "1164", "line": 2, "column": 18, "nodeType": "1091", "messageId": "1092", "endLine": 2, "endColumn": 28}, {"ruleId": "1089", "severity": 1, "message": "1105", "line": 11, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 11, "endColumn": 10}, {"ruleId": "1089", "severity": 1, "message": "1193", "line": 13, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 13, "endColumn": 11}, {"ruleId": "1089", "severity": 1, "message": "1314", "line": 25, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 25, "endColumn": 17}, {"ruleId": "1089", "severity": 1, "message": "1315", "line": 30, "column": 11, "nodeType": "1091", "messageId": "1092", "endLine": 30, "endColumn": 19}, {"ruleId": "1089", "severity": 1, "message": "1123", "line": 40, "column": 10, "nodeType": "1091", "messageId": "1092", "endLine": 40, "endColumn": 24}, {"ruleId": "1089", "severity": 1, "message": "1105", "line": 11, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 11, "endColumn": 10}, {"ruleId": "1089", "severity": 1, "message": "1135", "line": 12, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 12, "endColumn": 13}, {"ruleId": "1089", "severity": 1, "message": "1123", "line": 42, "column": 10, "nodeType": "1091", "messageId": "1092", "endLine": 42, "endColumn": 24}, {"ruleId": "1089", "severity": 1, "message": "1316", "line": 42, "column": 26, "nodeType": "1091", "messageId": "1092", "endLine": 42, "endColumn": 36}, {"ruleId": "1089", "severity": 1, "message": "1189", "line": 47, "column": 11, "nodeType": "1091", "messageId": "1092", "endLine": 47, "endColumn": 22}, {"ruleId": "1089", "severity": 1, "message": "1098", "line": 7, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 7, "endColumn": 8}, {"ruleId": "1089", "severity": 1, "message": "1228", "line": 10, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 10, "endColumn": 9}, {"ruleId": "1089", "severity": 1, "message": "1105", "line": 11, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 11, "endColumn": 10}, {"ruleId": "1089", "severity": 1, "message": "1218", "line": 19, "column": 12, "nodeType": "1091", "messageId": "1092", "endLine": 19, "endColumn": 21}, {"ruleId": "1089", "severity": 1, "message": "1317", "line": 8, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 8, "endColumn": 17}, {"ruleId": "1089", "severity": 1, "message": "1235", "line": 30, "column": 10, "nodeType": "1091", "messageId": "1092", "endLine": 30, "endColumn": 15}, {"ruleId": "1089", "severity": 1, "message": "1318", "line": 69, "column": 13, "nodeType": "1091", "messageId": "1092", "endLine": 69, "endColumn": 21}, {"ruleId": "1114", "severity": 1, "message": "1319", "line": 43, "column": 6, "nodeType": "1116", "endLine": 43, "endColumn": 20, "suggestions": "1320"}, {"ruleId": "1089", "severity": 1, "message": "1118", "line": 10, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 10, "endColumn": 14}, {"ruleId": "1089", "severity": 1, "message": "1119", "line": 11, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 11, "endColumn": 13}, {"ruleId": "1089", "severity": 1, "message": "1120", "line": 12, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 12, "endColumn": 9}, {"ruleId": "1089", "severity": 1, "message": "1193", "line": 13, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 13, "endColumn": 11}, {"ruleId": "1089", "severity": 1, "message": "1321", "line": 16, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 16, "endColumn": 12}, {"ruleId": "1089", "severity": 1, "message": "1135", "line": 23, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 23, "endColumn": 13}, {"ruleId": "1089", "severity": 1, "message": "1095", "line": 24, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 24, "endColumn": 10}, {"ruleId": "1089", "severity": 1, "message": "1321", "line": 20, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 20, "endColumn": 12}, {"ruleId": "1089", "severity": 1, "message": "1322", "line": 33, "column": 17, "nodeType": "1091", "messageId": "1092", "endLine": 33, "endColumn": 25}, {"ruleId": "1089", "severity": 1, "message": "1323", "line": 34, "column": 17, "nodeType": "1091", "messageId": "1092", "endLine": 34, "endColumn": 27}, {"ruleId": "1114", "severity": 1, "message": "1324", "line": 55, "column": 6, "nodeType": "1116", "endLine": 55, "endColumn": 17, "suggestions": "1325"}, {"ruleId": "1089", "severity": 1, "message": "1189", "line": 48, "column": 11, "nodeType": "1091", "messageId": "1092", "endLine": 48, "endColumn": 22}, {"ruleId": "1114", "severity": 1, "message": "1326", "line": 87, "column": 6, "nodeType": "1116", "endLine": 87, "endColumn": 64, "suggestions": "1327"}, {"ruleId": "1114", "severity": 1, "message": "1328", "line": 80, "column": 6, "nodeType": "1116", "endLine": 80, "endColumn": 50, "suggestions": "1329"}, {"ruleId": "1089", "severity": 1, "message": "1105", "line": 13, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 13, "endColumn": 10}, {"ruleId": "1330", "severity": 1, "message": "1331", "line": 129, "column": 27, "nodeType": "1332", "messageId": "1333", "endLine": 129, "endColumn": 28, "suggestions": "1334"}, {"ruleId": "1330", "severity": 1, "message": "1335", "line": 129, "column": 29, "nodeType": "1332", "messageId": "1333", "endLine": 129, "endColumn": 30, "suggestions": "1336"}, {"ruleId": "1330", "severity": 1, "message": "1337", "line": 129, "column": 31, "nodeType": "1332", "messageId": "1333", "endLine": 129, "endColumn": 32, "suggestions": "1338"}, {"ruleId": "1089", "severity": 1, "message": "1105", "line": 13, "column": 3, "nodeType": "1091", "messageId": "1092", "endLine": 13, "endColumn": 10}, {"ruleId": "1089", "severity": 1, "message": "1339", "line": 17, "column": 8, "nodeType": "1091", "messageId": "1092", "endLine": 17, "endColumn": 27}, "no-unused-vars", "'Parametres' is defined but never used.", "Identifier", "unusedVar", "'EntrepriseDocumentView' is defined but never used.", "'PowerBIDashboard' is defined but never used.", "'Tooltip' is defined but never used.", "'setOpen' is assigned a value but never used.", "'LinearProgress' is defined but never used.", "'Paper' is defined but never used.", "'Tab' is defined but never used.", "'Tabs' is defined but never used.", "'ScheduleIcon' is defined but never used.", "'Legend' is defined but never used.", "'tabValue' is assigned a value but never used.", "'handleTabChange' is assigned a value but never used.", "'Divider' is defined but never used.", "'CardHeader' is defined but never used.", "'Badge' is defined but never used.", "'VisibilityIcon' is defined but never used.", "'PaymentIcon' is defined but never used.", "'FilterListIcon' is defined but never used.", "'MoreVertIcon' is defined but never used.", "'getStatusColor' is assigned a value but never used.", "'getStatusTextColor' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'filterFactures'. Either include it or remove the dependency array.", "ArrayExpression", ["1340"], "'FormControl' is defined but never used.", "'InputLabel' is defined but never used.", "'Select' is defined but never used.", "'ReceiptIcon' is defined but never used.", "'DescriptionIcon' is defined but never used.", "'formatCurrency' is defined but never used.", "'clients' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchVendeurs'. Either include it or remove the dependency array.", ["1341"], "React Hook useEffect has a missing dependency: 'applyFiltersAndSort'. Either include it or remove the dependency array.", ["1342"], "React Hook useEffect has a missing dependency: 'getRoutePrefix'. Either include it or remove the dependency array.", ["1343"], "'handleOpenMenu' is assigned a value but never used.", "'generateAvatar' is assigned a value but never used.", "'logoFile' is assigned a value but never used.", "'useEffect' is defined but never used.", "'IconButton' is defined but never used.", "React Hook useEffect has a missing dependency: 'filterDevis'. Either include it or remove the dependency array.", ["1344"], "'Dialog' is defined but never used.", "'WarningIcon' is defined but never used.", "'CheckCircleIcon' is defined but never used.", "React Hook useEffect has a missing dependency: 'filterProduits'. Either include it or remove the dependency array.", ["1345"], "'Box' is defined but never used.", "'LanguageIcon' is defined but never used.", "'CategoryIcon' is defined but never used.", "'NotesIcon' is defined but never used.", "'StatutBadge' is defined but never used.", "'getTemplateSettings' is defined but never used.", "'getDefaultCurrency' is defined but never used.", "no-dupe-keys", "Duplicate key 'getStockHistory'.", "ObjectExpression", "unexpected", "'LockIcon' is defined but never used.", "'result' is assigned a value but never used.", "'handleCreateFacture' is assigned a value but never used.", "'templateError' is assigned a value but never used.", "The ref value 'documentRef.current' will likely have changed by the time this effect cleanup function runs. If this ref points to a node rendered by React, copy 'documentRef.current' to a variable inside the effect, and use that variable in the cleanup function.", "'Switch' is defined but never used.", "no-throw-literal", "Expected an error object to be thrown.", "ThrowStatement", "object", "'RouterLink' is defined but never used.", "'Container' is defined but never used.", "'Link' is defined but never used.", "'PersonIcon' is defined but never used.", "'BusinessIcon' is defined but never used.", "'RadioGroup' is defined but never used.", "'FormControlLabel' is defined but never used.", "'Radio' is defined but never used.", "'AnimatePresence' is defined but never used.", "'handleRoleChange' is assigned a value but never used.", "'enqueueSnackbar' is assigned a value but never used.", "'SupervisorAccountIcon' is defined but never used.", "'DateRangeIcon' is defined but never used.", "'DashboardIcon' is defined but never used.", "'FolderIcon' is defined but never used.", "'isActive' is assigned a value but never used.", "'isActiveGroup' is assigned a value but never used.", "'AddIcon' is defined but never used.", "'theme' is assigned a value but never used.", "'newDocumentAnchorEl' is assigned a value but never used.", "'handleNewDocumentOpen' is assigned a value but never used.", "'handleNewInvoice' is assigned a value but never used.", "'handleNewQuote' is assigned a value but never used.", "'RefreshIcon' is defined but never used.", "'mockUsers' is assigned a value but never used.", "'currentUser' is assigned a value but never used.", "'userToDelete' is assigned a value but never used.", "'Chip' is defined but never used.", "'Menu' is defined but never used.", "'MenuItem' is defined but never used.", "'ListItemIcon' is defined but never used.", "'CalendarIcon' is defined but never used.", "'mockVendeurs' is assigned a value but never used.", "'vendeurToDelete' is assigned a value but never used.", "'anchorEl' is assigned a value but never used.", "'handleMenuOpen' is assigned a value but never used.", "'handleMenuClose' is assigned a value but never used.", "'handleViewDetails' is assigned a value but never used.", "'mockEntreprises' is assigned a value but never used.", "'entrepriseToDelete' is assigned a value but never used.", "'EmailIcon' is defined but never used.", "'navigate' is assigned a value but never used.", "'changeLanguage' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'language'. Either include it or remove the dependency array.", ["1346"], "'handleSwitchChange' is assigned a value but never used.", "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration", ["1347"], "Duplicate key 'company_name'.", "'DialogTitle' is defined but never used.", "'CardIcon' is defined but never used.", "'PrintIcon' is defined but never used.", "'ErrorIcon' is defined but never used.", "'ArrowUpwardIcon' is defined but never used.", "'ArrowDownwardIcon' is defined but never used.", "'isMobile' is assigned a value but never used.", "'isTablet' is assigned a value but never used.", "'loading' is assigned a value but never used.", ["1348"], "'TrendingDownIcon' is defined but never used.", "'activeTab' is assigned a value but never used.", "'stats' is assigned a value but never used.", "'Button' is defined but never used.", "'DialogContent' is defined but never used.", "'DialogActions' is defined but never used.", "'Alert' is defined but never used.", "'ListItemText' is defined but never used.", "'ListItemSecondaryAction' is defined but never used.", "'FilterIcon' is defined but never used.", "'error' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'filterPaiements'. Either include it or remove the dependency array.", ["1349"], "'facture' is assigned a value but never used.", "'factureService' is defined but never used.", "'Stack' is defined but never used.", "'PeopleAltIcon' is defined but never used.", "'TrendingUpIcon' is defined but never used.", "'CircularProgress' is defined but never used.", "'Fade' is defined but never used.", "'Zoom' is defined but never used.", "'RadarChart' is defined but never used.", "'Radar' is defined but never used.", "'PolarGrid' is defined but never used.", "'PolarAngleAxis' is defined but never used.", "'PolarRadiusAxis' is defined but never used.", "'GetAppIcon' is defined but never used.", "'TableChartIcon' is defined but never used.", "'InfoIcon' is defined but never used.", "'HelpIcon' is defined but never used.", "'dateRange' is assigned a value but never used.", "'handlePeriodChange' is assigned a value but never used.", "'handleChartTypeChange' is assigned a value but never used.", "'handleSearch' is assigned a value but never used.", "'handleDateRangeChange' is assigned a value but never used.", "'FileDownloadIcon' is defined but never used.", "'CloseIcon' is defined but never used.", "'AdapterDateFns' is defined but never used.", "'LocalizationProvider' is defined but never used.", "'DatePicker' is defined but never used.", "'fr' is defined but never used.", "'productStats' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'fetchDashboardData' and 'timeRange'. Either include them or remove the dependency array.", ["1350"], "'exportToCSV' is defined but never used.", "'refreshing' is assigned a value but never used.", ["1351"], "React Hook useEffect has a missing dependency: 'filterVendeurs'. Either include it or remove the dependency array.", ["1352"], "'ButtonGroup' is defined but never used.", "'DownloadIcon' is defined but never used.", "'ShowChartIcon' is defined but never used.", "'BarChartIcon' is defined but never used.", "'BarChart' is defined but never used.", "'Bar' is defined but never used.", "'XAxis' is defined but never used.", "'YAxis' is defined but never used.", "'CartesianGrid' is defined but never used.", "'RechartsTooltip' is defined but never used.", "'ResponsiveContainer' is defined but never used.", "'AreaChart' is defined but never used.", "'Area' is defined but never used.", "'PieChart' is defined but never used.", "'Pie' is defined but never used.", "'Cell' is defined but never used.", "'COLORS' is assigned a value but never used.", "'paiements' is assigned a value but never used.", "'chartType' is assigned a value but never used.", "'paymentData' is assigned a value but never used.", "React Hook useCallback has a missing dependency: 'refreshing'. Either include it or remove the dependency array.", ["1353"], "'CardContent' is defined but never used.", "React Hook useEffect has a missing dependency: 'applyFilters'. Either include it or remove the dependency array.", ["1354"], ["1355"], "'Snackbar' is defined but never used.", "'DialogContentText' is defined but never used.", "'Business' is defined but never used.", "'Edit' is defined but never used.", "'successMessage' is assigned a value but never used.", "'setSuccessMessage' is assigned a value but never used.", "'List' is defined but never used.", "'ListItem' is defined but never used.", "'formatStatut' is defined but never used.", "'getStatusIcon' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchData'. Either include it or remove the dependency array.", ["1356"], "React Hook useEffect has a missing dependency: 'filteredPaiements'. Either include it or remove the dependency array.", ["1357"], "'InputAdornment' is defined but never used.", "'SaveIcon' is defined but never used.", "'formatDate' is defined but never used.", "'FormHelperText' is defined but never used.", "'response' is assigned a value but never used.", "React Hook useCallback has a missing dependency: 'updateRemainingTime'. Either include it or remove the dependency array.", ["1358"], "'CardMedia' is defined but never used.", "'ViewIcon' is defined but never used.", "'AssignIcon' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchTemplates'. Either include it or remove the dependency array.", ["1359"], "React Hook useEffect has a missing dependency: 'loadBonLivraisons'. Either include it or remove the dependency array.", ["1360"], "React Hook useEffect has a missing dependency: 'loadLivreurs'. Either include it or remove the dependency array.", ["1361"], "no-useless-escape", "Unnecessary escape character: \\+.", "Literal", "unnecessaryEscape", ["1362", "1363"], "Unnecessary escape character: \\(.", ["1364", "1365"], "Unnecessary escape character: \\).", ["1366", "1367"], "'bonLivraisonService' is defined but never used.", {"desc": "1368", "fix": "1369"}, {"desc": "1370", "fix": "1371"}, {"desc": "1372", "fix": "1373"}, {"desc": "1374", "fix": "1375"}, {"desc": "1376", "fix": "1377"}, {"desc": "1378", "fix": "1379"}, {"desc": "1380", "fix": "1381"}, {"desc": "1380", "fix": "1382"}, {"desc": "1383", "fix": "1384"}, {"desc": "1385", "fix": "1386"}, {"desc": "1387", "fix": "1388"}, {"desc": "1370", "fix": "1389"}, {"desc": "1390", "fix": "1391"}, {"desc": "1392", "fix": "1393"}, {"desc": "1394", "fix": "1395"}, {"desc": "1396", "fix": "1397"}, {"desc": "1398", "fix": "1399"}, {"desc": "1400", "fix": "1401"}, {"desc": "1402", "fix": "1403"}, {"desc": "1404", "fix": "1405"}, {"desc": "1406", "fix": "1407"}, {"desc": "1408", "fix": "1409"}, {"messageId": "1410", "fix": "1411", "desc": "1412"}, {"messageId": "1413", "fix": "1414", "desc": "1415"}, {"messageId": "1410", "fix": "1416", "desc": "1412"}, {"messageId": "1413", "fix": "1417", "desc": "1415"}, {"messageId": "1410", "fix": "1418", "desc": "1412"}, {"messageId": "1413", "fix": "1419", "desc": "1415"}, "Update the dependencies array to be: [factures, searchTerm, statutFilter, clientFilter, filterFactures]", {"range": "1420", "text": "1421"}, "Update the dependencies array to be: [fetchVendeurs]", {"range": "1422", "text": "1423"}, "Update the dependencies array to be: [clientsWithStats, searchTerm, categoryFilter, statusFilter, vendeurFilter, sortBy, applyFiltersAndSort]", {"range": "1424", "text": "1425"}, "Update the dependencies array to be: [getRoutePrefix, location, navigate]", {"range": "1426", "text": "1427"}, "Update the dependencies array to be: [devis, searchTerm, statutFilter, clientFilter, filterDevis]", {"range": "1428", "text": "1429"}, "Update the dependencies array to be: [filterProduits, produits, searchTerm, selectedCategory]", {"range": "1430", "text": "1431"}, "Update the dependencies array to be: [language]", {"range": "1432", "text": "1433"}, {"range": "1434", "text": "1433"}, "Update the dependencies array to be: [factures, searchTerm, statusFilter, modeFilter, filterFactures]", {"range": "1435", "text": "1436"}, "Update the dependencies array to be: [searchTerm, statusFilter, modeFilter, paiements, tabValue, filterPaiements]", {"range": "1437", "text": "1438"}, "Update the dependencies array to be: [fetchDashboardData, timeRange]", {"range": "1439", "text": "1440"}, {"range": "1441", "text": "1423"}, "Update the dependencies array to be: [vendeurs, searchTerm, activeFilter, filterVendeurs]", {"range": "1442", "text": "1443"}, "Update the dependencies array to be: [currentUser.clientId, currentUser.email, refreshing]", {"range": "1444", "text": "1445"}, "Update the dependencies array to be: [searchTerm, statusFilter, dateFilter, factures, applyFilters]", {"range": "1446", "text": "1447"}, "Update the dependencies array to be: [searchTerm, statusFilter, dateFilter, devis, applyFilters]", {"range": "1448", "text": "1449"}, "Update the dependencies array to be: [currentUser, dateRange, customDateRange, fetchData]", {"range": "1450", "text": "1451"}, "Update the dependencies array to be: [filteredPaiements, searchTerm]", {"range": "1452", "text": "1453"}, "Update the dependencies array to be: [abonnementId, updateRemainingTime]", {"range": "1454", "text": "1455"}, "Update the dependencies array to be: [activeTab, fetchTemplates]", {"range": "1456", "text": "1457"}, "Update the dependencies array to be: [searchTerm, statutFilter, dateDebutFilter, dateFinFilter, loadBonLivraisons]", {"range": "1458", "text": "1459"}, "Update the dependencies array to be: [searchTerm, statutFilter, disponibleFilter, loadLivreurs]", {"range": "1460", "text": "1461"}, "removeEscape", {"range": "1462", "text": "1463"}, "Remove the `\\`. This maintains the current functionality.", "escape<PERSON><PERSON><PERSON><PERSON>", {"range": "1464", "text": "1465"}, "Replace the `\\` with `\\\\` to include the actual backslash character.", {"range": "1466", "text": "1463"}, {"range": "1467", "text": "1465"}, {"range": "1468", "text": "1463"}, {"range": "1469", "text": "1465"}, [4379, 4429], "[factures, searchTerm, statutFilter, clientFilter, filterFactures]", [3621, 3623], "[fetchVendeurs]", [3683, 3766], "[clientsWithStats, searchTerm, categoryFilter, statusFilter, vendeurFilter, sortBy, applyFiltersAndSort]", [4629, 4649], "[getRoutePrefix, location, navigate]", [4938, 4985], "[devis, searchTerm, statutFilter, clientFilter, filterDevis]", [2764, 2804], "[filterProduits, produits, searchTerm, selectedCategory]", [3455, 3457], "[language]", [1805, 1807], [7917, 7965], "[factures, searchTerm, statusFilter, modeFilter, filterFactures]", [4437, 4496], "[searchTerm, statusFilter, modeFilter, paiements, tabValue, filterPaiements]", [11902, 11904], "[fetchDashboardData, timeRange]", [2900, 2902], [2953, 2989], "[vendeurs, searchTerm, activeFilter, filterVendeurs]", [18965, 18978], "[currentUser.clientId, currentUser.email, refreshing]", [6253, 6301], "[searchTerm, statusFilter, dateFilter, factures, applyFilters]", [6650, 6695], "[searchTerm, statusFilter, dateFilter, devis, applyFilters]", [9086, 9127], "[currentUser, dateRange, customDateRange, fetchData]", [9753, 9765], "[filteredPaiements, searchTerm]", [1491, 1505], "[abonnementId, updateRemainingTime]", [1176, 1187], "[activeTab, fetchTemplates]", [2458, 2516], "[searchTerm, statutFilter, dateDebutFilter, dateFinFilter, loadBonLivraisons]", [2079, 2123], "[searchTerm, statutFilter, disponibleFilter, loadLivreurs]", [3782, 3783], "", [3782, 3782], "\\", [3784, 3785], [3784, 3784], [3786, 3787], [3786, 3786]]