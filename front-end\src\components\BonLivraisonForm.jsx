import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  TextField,
  Button,
  Typography,
  Autocomplete,
  Alert,
  CircularProgress,
  Divider,
  Chip
} from '@mui/material';

import bonLivraisonService from '../services/bonLivraisonService';
import livreurService from '../services/livreurService';
import factureService from '../services/factureService';

const BonLivraisonForm = ({ bonLivraison, onSubmit, onCancel }) => {
  const [formData, setFormData] = useState({
    livreurId: '',
    factureId: ''
  });

  const [errors, setErrors] = useState({});
  const [loading, setLoading] = useState(false);
  const [dataLoading, setDataLoading] = useState(true);
  const [livreurs, setLivreurs] = useState([]);
  const [factures, setFactures] = useState([]);
  const [selectedFacture, setSelectedFacture] = useState(null);
  const [loadingError, setLoadingError] = useState(null);

  // Charger les données nécessaires
  useEffect(() => {
    const loadData = async () => {
      try {
        setDataLoading(true);
        setLoadingError(null);
        console.log('🔄 Chargement des données pour le formulaire...');

        const [livreursData, facturesData] = await Promise.all([
          livreurService.getLivreursDisponibles(),
          factureService.getFactures()
        ]);

        console.log('✅ Données chargées:', {
          livreurs: livreursData?.length || 0,
          factures: facturesData?.length || 0
        });

        setLivreurs(livreursData || []);
        // Filtrer seulement les factures payées ou envoyées
        const facturesDisponibles = (facturesData || []).filter(f =>
          f.statut === 'PAID' || f.statut === 'SENT'
        );
        setFactures(facturesDisponibles);
      } catch (error) {
        console.error('❌ Erreur lors du chargement des données:', error);
        setLoadingError('Erreur lors du chargement des données. Veuillez réessayer.');
        setLivreurs([]);
        setFactures([]);
      } finally {
        setDataLoading(false);
      }
    };

    loadData();
  }, []);

  // Initialiser le formulaire avec les données du bon de livraison si en mode édition
  useEffect(() => {
    if (bonLivraison) {
      setFormData({
        livreurId: bonLivraison.livreurId?._id || '',
        factureId: bonLivraison.factureId?._id || ''
      });

      if (bonLivraison.factureId) {
        setSelectedFacture(bonLivraison.factureId);
      }
    }
  }, [bonLivraison]);

  // Gérer les changements de champs
  const handleChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // Effacer l'erreur pour ce champ
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  // Gérer les changements de facture
  const handleFactureChange = (factureId) => {
    const facture = factures.find(f => f._id === factureId);
    setSelectedFacture(facture);
    handleChange('factureId', factureId);
  };

  // Formater le montant
  const formatMontant = (montant) => {
    if (!montant) return '0.00 DT';
    return new Intl.NumberFormat('fr-TN', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(montant) + ' DT';
  };

  // Valider et soumettre le formulaire
  const handleSubmit = async (e) => {
    e.preventDefault();

    // Validation complète
    const newErrors = {};
    if (!formData.livreurId) {
      newErrors.livreurId = 'Le livreur est obligatoire';
    }
    if (!formData.factureId) {
      newErrors.factureId = 'La facture est obligatoire';
    }
    if (!selectedFacture) {
      newErrors.factureId = 'Veuillez sélectionner une facture valide';
    }

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    setLoading(true);
    try {
      // Préparer les données pour la soumission avec tous les champs requis
      const submissionData = {
        ...formData,
        // Informations de base
        clientId: selectedFacture?.clientId?._id || selectedFacture?.clientId,
        montantTotal: selectedFacture?.total || 0,
        dateLivraison: new Date().toISOString().split('T')[0], // Date du jour par défaut
        statut: 'EN_PREPARATION',

        // Adresse de livraison (récupérée du client de la facture)
        adresseLivraison: {
          adresse: selectedFacture?.clientId?.adresse || 'Adresse non spécifiée',
          ville: selectedFacture?.clientId?.ville || '',
          codePostal: selectedFacture?.clientId?.codePostal || '',
          instructions: ''
        },

        // Lignes de produits (récupérées de la facture)
        lignes: selectedFacture?.lignes?.map((ligne, index) => ({
          produitId: ligne.produitId || ligne._id || `temp_product_${index}`,
          nomProduit: ligne.nomProduit || ligne.nom || ligne.description || `Produit ${index + 1}`,
          description: ligne.description || ligne.nomProduit || ligne.nom || '',
          quantiteLivree: ligne.quantite || 1,
          quantiteCommandee: ligne.quantite || 1,
          unite: ligne.unite || 'unité',
          prixUnitaire: ligne.prixUnitaire || 0,
          montantLigne: ligne.montantLigne || (ligne.quantite * ligne.prixUnitaire) || 0,
          notes: ''
        })) || [],

        // Montants
        montantHT: selectedFacture?.sousTotal || 0,
        montantTTC: selectedFacture?.total || 0,
        tauxTVA: selectedFacture?.tauxTVA || 19,

        // Notes
        notes: `Bon de livraison généré automatiquement pour la facture ${selectedFacture?.numero || ''}`
      };

      console.log('📦 Données de soumission du bon de livraison:', submissionData);
      console.log('📦 Lignes détaillées:', JSON.stringify(submissionData.lignes, null, 2));
      console.log('📦 Adresse de livraison:', JSON.stringify(submissionData.adresseLivraison, null, 2));
      await onSubmit(submissionData);
    } catch (error) {
      console.error('Erreur lors de la soumission:', error);
      console.error('Détails de l\'erreur backend:', error.response?.data);

      // Afficher les erreurs spécifiques du backend
      let errorMessage = 'Erreur lors de la création du bon de livraison';
      if (error.response?.data?.errors && Array.isArray(error.response.data.errors)) {
        errorMessage = error.response.data.errors.join(', ');
      } else if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      }

      setErrors({
        submit: errorMessage
      });
    } finally {
      setLoading(false);
    }
  };

  // Affichage du chargement initial
  if (dataLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 200 }}>
        <CircularProgress />
        <Typography variant="body1" sx={{ ml: 2 }}>
          Chargement des données...
        </Typography>
      </Box>
    );
  }

  return (
    <Box component="form" onSubmit={handleSubmit} sx={{ mt: 2 }}>
      {/* Affichage des erreurs de chargement */}
      {loadingError && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {loadingError}
        </Alert>
      )}

      {/* Affichage des erreurs de soumission */}
      {errors.submit && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {errors.submit}
        </Alert>
      )}

      {/* Affichage des informations de débogage */}
      {process.env.NODE_ENV === 'development' && (
        <Alert severity="info" sx={{ mb: 2 }}>
          Données disponibles: {livreurs.length} livreurs, {factures.length} factures
        </Alert>
      )}

      <Grid container spacing={3}>
        {/* Sélection de la facture */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Sélection de la facture
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <Autocomplete
                    options={factures}
                    getOptionLabel={(option) => `${option.numero} - ${option.clientId?.nom || 'Client inconnu'}`}
                    value={factures.find(f => f._id === formData.factureId) || null}
                    onChange={(event, newValue) => {
                      handleFactureChange(newValue?._id || '');
                    }}
                    noOptionsText={factures.length === 0 ? "Aucune facture disponible" : "Aucune facture trouvée"}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        label="Facture *"
                        error={!!errors.factureId}
                        helperText={errors.factureId || (factures.length === 0 ? "Aucune facture disponible" : "")}
                      />
                    )}
                  />
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Informations de la facture sélectionnée */}
        {selectedFacture && (
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Informations de la facture
                </Typography>
                <Grid container spacing={2}>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Numéro de facture"
                      value={selectedFacture.numero || ''}
                      InputProps={{ readOnly: true }}
                      variant="outlined"
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Date de facture"
                      value={selectedFacture.dateEmission ?
                        new Date(selectedFacture.dateEmission).toLocaleDateString('fr-FR') : ''}
                      InputProps={{ readOnly: true }}
                      variant="outlined"
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Client"
                      value={selectedFacture.clientId?.nom || 'Client inconnu'}
                      InputProps={{ readOnly: true }}
                      variant="outlined"
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Montant total"
                      value={formatMontant(selectedFacture.total)}
                      InputProps={{ readOnly: true }}
                      variant="outlined"
                    />
                  </Grid>
                  {selectedFacture.lignes && selectedFacture.lignes.length > 0 && (
                    <Grid item xs={12}>
                      <Typography variant="subtitle2" gutterBottom>
                        Produits à livrer:
                      </Typography>
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                        {selectedFacture.lignes.map((ligne, index) => (
                          <Chip
                            key={index}
                            label={`${ligne.description || 'Produit'} (Qté: ${ligne.quantite})`}
                            variant="outlined"
                            size="small"
                          />
                        ))}
                      </Box>
                    </Grid>
                  )}
                </Grid>
              </CardContent>
            </Card>
          </Grid>
        )}

        {/* Sélection du livreur */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Livreur
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <Autocomplete
                    options={livreurs}
                    getOptionLabel={(option) => `${option.prenom} ${option.nom}` || ''}
                    value={livreurs.find(l => l._id === formData.livreurId) || null}
                    onChange={(event, newValue) => {
                      handleChange('livreurId', newValue?._id || '');
                    }}
                    noOptionsText={livreurs.length === 0 ? "Aucun livreur disponible" : "Aucun livreur trouvé"}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        label="Livreur *"
                        error={!!errors.livreurId}
                        helperText={errors.livreurId || (livreurs.length === 0 ? "Aucun livreur disponible" : "")}
                      />
                    )}
                  />
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Boutons d'action */}
        <Grid item xs={12}>
          <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2, mt: 2 }}>
            <Button
              variant="outlined"
              onClick={onCancel}
              disabled={loading}
            >
              Annuler
            </Button>
            <Button
              type="submit"
              variant="contained"
              disabled={loading}
            >
              {loading ? 'Enregistrement...' : (bonLivraison ? 'Modifier' : 'Créer')}
            </Button>
          </Box>
        </Grid>
      </Grid>
    </Box>
  );
};

export default BonLivraisonForm;
