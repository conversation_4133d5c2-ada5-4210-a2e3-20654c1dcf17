const mongoose = require('mongoose');
const { Schema } = mongoose;

// Schéma pour les lignes de produits dans le bon de livraison
const ligneLivraisonSchema = new Schema({
    produitId: {
        type: Schema.Types.Mixed, // Permet ObjectId ou String pour plus de flexibilité
        required: true
    },
    nomProduit: { type: String, required: true },
    description: { type: String },
    quantiteLivree: {
        type: Number,
        required: true,
        min: [0, 'La quantité livrée ne peut pas être négative']
    },
    quantiteCommandee: {
        type: Number,
        required: true,
        min: [0, 'La quantité commandée ne peut pas être négative']
    },
    unite: { type: String, default: 'unité' },
    prixUnitaire: { type: Number, required: true },
    montantLigne: { type: Number, required: true },
    notes: { type: String }
}, { _id: true });

const bonLivraisonSchema = new Schema({
    // Informations de base
    numero: {
        type: String,
        required: [true, 'Le numéro est obligatoire'],
        unique: true,
        trim: true
    },

    // Relations
    clientId: {
        type: Schema.Types.ObjectId,
        ref: 'Client',
        required: [true, 'Le client est obligatoire']
    },
    livreurId: {
        type: Schema.Types.ObjectId,
        ref: 'Livreur',
        required: [true, 'Le livreur est obligatoire']
    },
    vendeurId: {
        type: Schema.Types.ObjectId,
        ref: 'users'
    },
    responsableId: {
        type: Schema.Types.ObjectId,
        ref: 'users',
        required: [true, 'Le responsable est obligatoire']
    },

    // Documents liés (optionnel)
    factureId: {
        type: Schema.Types.ObjectId,
        ref: 'Facture'
    },
    devisId: {
        type: Schema.Types.ObjectId,
        ref: 'Devis'
    },

    // Dates
    dateLivraison: {
        type: Date,
        required: [true, 'La date de livraison est obligatoire']
    },
    heureDepart: { type: String }, // Format HH:MM
    heureArrivee: { type: String }, // Format HH:MM
    dateCreation: { type: Date, default: Date.now },

    // Contenu de la livraison
    lignes: [ligneLivraisonSchema],

    // Statut de la livraison
    statut: {
        type: String,
        enum: [
            'EN_PREPARATION',
            'EN_COURS',
            'LIVREE',
            'PARTIELLEMENT_LIVREE',
            'ECHEC',
            'ANNULEE',
            'RETOURNEE'
        ],
        default: 'EN_PREPARATION'
    },

    // Informations de livraison
    adresseLivraison: {
        adresse: { type: String, required: true },
        ville: { type: String },
        codePostal: { type: String },
        instructions: { type: String } // Instructions spéciales pour la livraison
    },

    // Signature et confirmation
    signatureClient: {
        nom: { type: String },
        signature: { type: String }, // Base64 de la signature ou URL
        dateSigned: { type: Date }
    },

    // Montants
    montantTotal: { type: Number, required: true },
    montantHT: { type: Number },
    montantTTC: { type: Number },
    tauxTVA: { type: Number, default: 19 },

    // Notes et observations
    notes: { type: String },
    observationsLivreur: { type: String },
    motifEchec: { type: String }, // Si statut = ECHEC

    // Informations de suivi
    tracking: {
        numeroSuivi: { type: String },
        transporteur: { type: String },
        urlSuivi: { type: String }
    },

    // Photos de livraison (optionnel)
    photosLivraison: [{
        url: String,
        description: String,
        datePhoto: { type: Date, default: Date.now }
    }],

    // Timestamps
    dateMiseAJour: { type: Date, default: Date.now }
}, {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
});

// Index pour améliorer les performances
bonLivraisonSchema.index({ numero: 1 });
bonLivraisonSchema.index({ clientId: 1 });
bonLivraisonSchema.index({ livreurId: 1 });
bonLivraisonSchema.index({ responsableId: 1 });
bonLivraisonSchema.index({ dateLivraison: 1 });
bonLivraisonSchema.index({ statut: 1 });

// Middleware pour mettre à jour la date de modification
bonLivraisonSchema.pre('save', function(next) {
    this.dateMiseAJour = Date.now();

    // Calculer les montants automatiquement
    if (this.lignes && this.lignes.length > 0) {
        this.montantHT = this.lignes.reduce((total, ligne) => total + ligne.montantLigne, 0);
        this.montantTTC = this.montantHT * (1 + this.tauxTVA / 100);
        this.montantTotal = this.montantTTC;
    }

    next();
});

// Méthode pour générer un numéro automatique
bonLivraisonSchema.statics.genererNumero = async function(responsableId) {
    const prefix = 'BL-';
    const year = new Date().getFullYear();
    const count = await this.countDocuments({
        responsableId: responsableId,
        dateCreation: {
            $gte: new Date(year, 0, 1),
            $lt: new Date(year + 1, 0, 1)
        }
    });

    return `${prefix}${year}-${String(count + 1).padStart(4, '0')}`;
};

// Méthode pour vérifier si la livraison est complète
bonLivraisonSchema.methods.estLivraisonComplete = function() {
    return this.lignes.every(ligne => ligne.quantiteLivree >= ligne.quantiteCommandee);
};

// Méthode pour calculer le pourcentage de livraison
bonLivraisonSchema.methods.pourcentageLivraison = function() {
    if (!this.lignes || this.lignes.length === 0) return 0;

    const totalCommande = this.lignes.reduce((sum, ligne) => sum + ligne.quantiteCommandee, 0);
    const totalLivre = this.lignes.reduce((sum, ligne) => sum + ligne.quantiteLivree, 0);

    return totalCommande > 0 ? Math.round((totalLivre / totalCommande) * 100) : 0;
};

module.exports = mongoose.model('BonLivraison', bonLivraisonSchema);
