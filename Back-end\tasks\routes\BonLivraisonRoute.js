const express = require('express');
const router = express.Router();
const BonLivraison = require('../models/BonLivraisonModel');
const Livreur = require('../models/LivreurModel');
const Client = require('../models/ClientModel');
const User = require('../models/UserModel');
const Facture = require('../models/FactureModel');
const { verifyToken, isResponsable, isVendeur } = require('../middleware/authMiddleware');
const nodemailer = require('nodemailer');
const PDFDocument = require('pdfkit');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// Middleware pour vérifier que l'utilisateur peut accéder aux bons de livraison
const canAccessBonLivraison = [verifyToken, (req, res, next) => {
    if (req.userRole === 'RESPONSABLE' || req.userRole === 'VENDEUR' || req.userRole === 'ADMIN') {
        next();
    } else {
        return res.status(403).json({ message: 'Accès non autorisé' });
    }
}];

// GET /api/bon-livraisons - Récupérer tous les bons de livraison
router.get('/bon-livraisons', canAccessBonLivraison, async (req, res) => {
    try {
        let query = {};

        // Si l'utilisateur est un responsable, ne montrer que ses bons de livraison
        if (req.userRole === 'RESPONSABLE') {
            query.responsableId = req.userId;
        } else if (req.userRole === 'VENDEUR') {
            // Pour les vendeurs, montrer les bons qu'ils ont créés ou ceux de leur responsable
            const vendeur = await User.findById(req.userId).populate('responsables');
            if (vendeur && vendeur.responsables && vendeur.responsables.length > 0) {
                const responsableIds = vendeur.responsables.map(r => r._id);
                query.$or = [
                    { vendeurId: req.userId },
                    { responsableId: { $in: responsableIds } }
                ];
            } else {
                query.vendeurId = req.userId;
            }
        }

        const { statut, livreurId, clientId, dateDebut, dateFin, search } = req.query;

        // Filtres optionnels
        if (statut) {
            query.statut = statut;
        }
        if (livreurId) {
            query.livreurId = livreurId;
        }
        if (clientId) {
            query.clientId = clientId;
        }
        if (dateDebut || dateFin) {
            query.dateLivraison = {};
            if (dateDebut) query.dateLivraison.$gte = new Date(dateDebut);
            if (dateFin) query.dateLivraison.$lte = new Date(dateFin);
        }
        if (search) {
            query.$or = [
                { numero: { $regex: search, $options: 'i' } },
                { 'adresseLivraison.adresse': { $regex: search, $options: 'i' } },
                { notes: { $regex: search, $options: 'i' } }
            ];
        }

        const bonLivraisons = await BonLivraison.find(query)
            .populate('clientId', 'nom email telephone adresse')
            .populate('livreurId', 'nom prenom telephone vehicule')
            .populate('vendeurId', 'nom prenom')
            .populate('responsableId', 'nom prenom')
            .populate('factureId', 'numero')
            .populate('devisId', 'numéro')
            .sort({ dateCreation: -1 });

        res.status(200).json(bonLivraisons);
    } catch (error) {
        console.error('Erreur lors de la récupération des bons de livraison:', error);
        res.status(500).json({ message: 'Erreur serveur lors de la récupération des bons de livraison' });
    }
});

// Generate PDF for a bon de livraison (MUST BE BEFORE /:id route)
router.get("/bon-livraisons/:id/pdf", verifyToken, async (req, res) => {
  try {
    console.log(`Received GET /bon-livraisons/${req.params.id}/pdf request`);
    const bonLivraison = await BonLivraison.findById(req.params.id)
      .populate("clientId")
      .populate("livreurId")
      .populate("vendeurId")
      .populate("responsableId");

    if (!bonLivraison) return res.status(404).json({ error: "Bon de livraison not found" });

    // Generate PDF directly on the server
    const pdfBuffer = await generateBonLivraisonPDF(bonLivraison);

    // Set the response headers for PDF download
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `attachment; filename="bon-livraison-${bonLivraison.numero}.pdf"`);
    res.send(pdfBuffer);
  } catch (error) {
    console.error("Error generating PDF for bon de livraison:", error.message);
    res.status(500).json({ error: "Error while generating PDF for bon de livraison" });
  }
});

// Prepare bon de livraison for printing (MUST BE BEFORE /:id route)
router.get("/bon-livraisons/:id/print", verifyToken, async (req, res) => {
  try {
    console.log(`Received GET /bon-livraisons/${req.params.id}/print request`);
    const bonLivraison = await BonLivraison.findById(req.params.id)
      .populate("clientId")
      .populate("livreurId")
      .populate("vendeurId")
      .populate("responsableId");

    if (!bonLivraison) return res.status(404).json({ error: "Bon de livraison not found" });

    // Generate PDF directly on the server
    const pdfBuffer = await generateBonLivraisonPDF(bonLivraison);

    // Set the response headers for PDF (but not as attachment for printing)
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `inline; filename="bon-livraison-${bonLivraison.numero}.pdf"`);
    res.send(pdfBuffer);
  } catch (error) {
    console.error("Error preparing PDF for printing bon de livraison:", error.message);
    res.status(500).json({ error: "Error while preparing PDF for printing bon de livraison" });
  }
});

// Send bon de livraison by email (MUST BE BEFORE /:id route)
router.post("/bon-livraisons/:id/email", verifyToken, async (req, res) => {
  try {
    console.log(`Received POST /bon-livraisons/${req.params.id}/email request`);
    const bonLivraison = await BonLivraison.findById(req.params.id)
      .populate("clientId")
      .populate("livreurId")
      .populate("vendeurId")
      .populate("responsableId");

    if (!bonLivraison) return res.status(404).json({ error: "Bon de livraison not found" });

    // Get email data from request body
    let { to, subject, message } = req.body;

    if (!to) {
      // If no recipient is specified, use the client's email
      if (bonLivraison.clientId && bonLivraison.clientId.email) {
        to = bonLivraison.clientId.email;
      } else {
        return res.status(400).json({ error: "Recipient email is required" });
      }
    }

    // Handle multiple recipients
    let recipients = to;
    if (typeof to === 'string' && to.includes(',')) {
      recipients = to.split(',').map(email => email.trim()).filter(email => email);
      console.log("Multiple recipients detected:", recipients);
    }

    // Generate PDF
    const pdfBuffer = await generateBonLivraisonPDF(bonLivraison);

    // Create a nodemailer transporter
    const transporter = nodemailer.createTransporter({
      service: 'gmail',
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASSWORD
      },
      tls: {
        rejectUnauthorized: false
      }
    });

    // Get company information for email template
    let companyName = "Votre Entreprise";
    let primaryColor = "#1976d2";
    let emailSignature = "Cordialement,\nL'équipe de livraison";

    try {
      const Parametres = require('../models/ParametresModel');
      const ResponsableTemplate = require('../models/ResponsableTemplateModel');

      const params = await Parametres.findOne();
      let responsableTemplate = null;

      // Try to get responsable template settings based on the bon de livraison context
      let entrepriseId = null;

      // First, try to get enterprise ID from responsableId
      if (bonLivraison.responsableId) {
        entrepriseId = bonLivraison.responsableId;
        console.log('Email: Using responsableId as entrepriseId:', entrepriseId);
      }

      if (entrepriseId) {
        responsableTemplate = await ResponsableTemplate.findOne({ responsableId: entrepriseId });
        console.log('Email: Found responsable template:', responsableTemplate);
      }

      // Use company information from parameters or template
      if (params) {
        companyName = params.nomEntreprise || companyName;
        emailSignature = `Cordialement,\n${companyName}`;
      }

      if (responsableTemplate) {
        primaryColor = responsableTemplate.couleurPrimaire || primaryColor;
      }
    } catch (error) {
      console.error('Error getting company information for email:', error);
      // Continue with default values
    }

    // Email options
    const mailOptions = {
      from: `"${companyName}" <${process.env.EMAIL_FROM || process.env.EMAIL_USER}>`,
      to: recipients,
      subject: subject || `Bon de Livraison ${bonLivraison.numero}`,
      text: (message || `Veuillez trouver ci-joint le bon de livraison ${bonLivraison.numero}.`) + `\n\n${emailSignature}`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
            <h2 style="color: ${primaryColor}; margin: 0;">Bon de Livraison ${bonLivraison.numero}</h2>
            <div style="text-align: right;">
              <p style="margin: 0;">Date de livraison: ${bonLivraison.dateLivraison ? new Date(bonLivraison.dateLivraison).toLocaleDateString() : 'À définir'}</p>
            </div>
          </div>

          <div style="background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin-bottom: 20px;">
            <h3 style="margin-top: 0; color: ${primaryColor};">Informations de livraison</h3>
            <p><strong>Client:</strong> ${bonLivraison.clientId?.nom || 'N/A'}</p>
            <p><strong>Livreur:</strong> ${bonLivraison.livreurId?.nom || ''} ${bonLivraison.livreurId?.prenom || ''}</p>
            <p><strong>Statut:</strong> ${bonLivraison.statut}</p>
            <p><strong>Montant:</strong> ${bonLivraison.montantTotal?.toFixed(2) || '0.00'} DT</p>
          </div>

          <div style="margin-bottom: 20px;">
            <p>${message || `Veuillez trouver ci-joint le bon de livraison ${bonLivraison.numero}.`}</p>
          </div>

          <div style="border-top: 1px solid #ddd; padding-top: 15px; margin-top: 20px;">
            <p style="margin: 0; color: #666; font-size: 12px;">${emailSignature.replace(/\n/g, '<br>')}</p>
          </div>
        </div>
      `,
      attachments: [
        {
          filename: `bon-livraison-${bonLivraison.numero}.pdf`,
          content: pdfBuffer,
          contentType: 'application/pdf'
        }
      ]
    };

    // Send the email
    await transporter.sendMail(mailOptions);
    console.log("Email sent successfully to:", typeof recipients === 'object' ? recipients.join(', ') : recipients);

    res.status(200).json({
      message: "Email sent successfully",
      to,
      subject: mailOptions.subject
    });
  } catch (error) {
    console.error("Error sending email for bon de livraison:", error.message);
    res.status(500).json({ error: "Error while sending email for bon de livraison", details: error.message });
  }
});

// GET /api/bon-livraisons/:id - Récupérer un bon de livraison par ID
router.get('/bon-livraisons/:id', canAccessBonLivraison, async (req, res) => {
    try {
        const bonLivraison = await BonLivraison.findById(req.params.id)
            .populate('clientId', 'nom email telephone adresse')
            .populate('livreurId', 'nom prenom telephone vehicule')
            .populate('vendeurId', 'nom prenom')
            .populate('responsableId', 'nom prenom')
            .populate('factureId', 'numero')
            .populate('devisId', 'numéro')
            .populate('lignes.produitId', 'nom description');

        if (!bonLivraison) {
            return res.status(404).json({ message: 'Bon de livraison non trouvé' });
        }

        // Vérifier les permissions
        if (req.userRole === 'RESPONSABLE' && bonLivraison.responsableId._id.toString() !== req.userId) {
            return res.status(403).json({ message: 'Accès non autorisé à ce bon de livraison' });
        }
        if (req.userRole === 'VENDEUR' && bonLivraison.vendeurId && bonLivraison.vendeurId._id.toString() !== req.userId) {
            // Vérifier si le vendeur appartient au même responsable
            const vendeur = await User.findById(req.userId).populate('responsables');
            const responsableIds = vendeur.responsables.map(r => r._id.toString());
            if (!responsableIds.includes(bonLivraison.responsableId._id.toString())) {
                return res.status(403).json({ message: 'Accès non autorisé à ce bon de livraison' });
            }
        }

        res.status(200).json(bonLivraison);
    } catch (error) {
        console.error('Erreur lors de la récupération du bon de livraison:', error);
        res.status(500).json({ message: 'Erreur serveur lors de la récupération du bon de livraison' });
    }
});

// POST /api/bon-livraisons - Créer un nouveau bon de livraison (version simplifiée)
router.post('/bon-livraisons', canAccessBonLivraison, async (req, res) => {
    try {
        const {
            clientId,
            livreurId,
            factureId,
            devisId,
            dateLivraison,
            heureDepart,
            heureArrivee,
            lignes,
            adresseLivraison,
            notes,
            tracking,
            montantTotal,
            statut
        } = req.body;

        // Validation détaillée des champs obligatoires pour la version simplifiée
        const validationErrors = [];

        if (!livreurId) {
            validationErrors.push('Le livreur est obligatoire');
        }
        if (!factureId) {
            validationErrors.push('La facture est obligatoire');
        }

        if (validationErrors.length > 0) {
            return res.status(400).json({
                message: 'Données manquantes ou invalides',
                errors: validationErrors
            });
        }

        // Vérifier que le livreur existe et est disponible
        const livreur = await Livreur.findById(livreurId);
        if (!livreur) {
            return res.status(404).json({ message: 'Livreur non trouvé' });
        }
        if (!livreur.estDisponible()) {
            return res.status(400).json({ message: 'Le livreur sélectionné n\'est pas disponible' });
        }

        // Vérifier que la facture existe et récupérer les informations
        const facture = await Facture.findById(factureId).populate('clientId');
        if (!facture) {
            return res.status(404).json({ message: 'Facture non trouvée' });
        }

        // Utiliser le client de la facture si clientId n'est pas fourni
        const finalClientId = clientId || facture.clientId._id;
        const client = facture.clientId;

        // Déterminer le responsable
        let responsableId;
        if (req.userRole === 'RESPONSABLE') {
            responsableId = req.userId;
        } else if (req.userRole === 'VENDEUR') {
            const vendeur = await User.findById(req.userId).populate('responsables');
            if (vendeur && vendeur.responsables && vendeur.responsables.length > 0) {
                responsableId = vendeur.responsables[0]._id;
            } else {
                return res.status(400).json({ message: 'Vendeur non associé à un responsable' });
            }
        }

        // Générer un numéro automatique
        const numero = await BonLivraison.genererNumero(responsableId);

        // Créer les lignes à partir de la facture si elles ne sont pas fournies
        const finalLignes = lignes || facture.lignes.map(ligne => ({
            produitId: ligne.produit,
            nomProduit: ligne.description,
            description: ligne.description,
            quantiteCommandee: ligne.quantite,
            quantiteLivree: ligne.quantite, // Par défaut, on livre tout
            unite: 'unité',
            prixUnitaire: ligne.prixUnitaire,
            montantLigne: ligne.montantHT
        }));

        const nouveauBonLivraison = new BonLivraison({
            numero,
            clientId: finalClientId,
            livreurId,
            vendeurId: req.userRole === 'VENDEUR' ? req.userId : undefined,
            responsableId,
            factureId,
            devisId,
            dateLivraison: dateLivraison || new Date(),
            heureDepart,
            heureArrivee,
            lignes: finalLignes,
            adresseLivraison: adresseLivraison || {
                adresse: client.adresse,
                ville: client.ville,
                codePostal: client.codePostal
            },
            notes,
            tracking,
            montantTotal: montantTotal || facture.total,
            statut: statut || 'EN_PREPARATION'
        });

        const bonLivraisonSauvegarde = await nouveauBonLivraison.save();
        await bonLivraisonSauvegarde.populate([
            { path: 'clientId', select: 'nom email telephone adresse' },
            { path: 'livreurId', select: 'nom prenom telephone vehicule' },
            { path: 'vendeurId', select: 'nom prenom' },
            { path: 'responsableId', select: 'nom prenom' }
        ]);

        res.status(201).json({
            message: 'Bon de livraison créé avec succès',
            bonLivraison: bonLivraisonSauvegarde
        });
    } catch (error) {
        console.error('Erreur lors de la création du bon de livraison:', error);
        if (error.name === 'ValidationError') {
            return res.status(400).json({
                message: 'Données invalides',
                errors: Object.values(error.errors).map(e => e.message)
            });
        }
        res.status(500).json({ message: 'Erreur serveur lors de la création du bon de livraison' });
    }
});

// PUT /api/bon-livraisons/:id - Mettre à jour un bon de livraison
router.put('/bon-livraisons/:id', canAccessBonLivraison, async (req, res) => {
    try {
        const bonLivraison = await BonLivraison.findById(req.params.id);

        if (!bonLivraison) {
            return res.status(404).json({ message: 'Bon de livraison non trouvé' });
        }

        // Vérifier les permissions
        if (req.userRole === 'RESPONSABLE' && bonLivraison.responsableId.toString() !== req.userId) {
            return res.status(403).json({ message: 'Accès non autorisé à ce bon de livraison' });
        }
        if (req.userRole === 'VENDEUR' && bonLivraison.vendeurId && bonLivraison.vendeurId.toString() !== req.userId) {
            // Vérifier si le vendeur appartient au même responsable
            const vendeur = await User.findById(req.userId).populate('responsables');
            const responsableIds = vendeur.responsables.map(r => r._id.toString());
            if (!responsableIds.includes(bonLivraison.responsableId.toString())) {
                return res.status(403).json({ message: 'Accès non autorisé à ce bon de livraison' });
            }
        }

        const {
            livreurId,
            dateLivraison,
            heureDepart,
            heureArrivee,
            lignes,
            statut,
            adresseLivraison,
            signatureClient,
            notes,
            observationsLivreur,
            motifEchec,
            tracking,
            photosLivraison
        } = req.body;

        // Vérifier le livreur si changé
        if (livreurId && livreurId !== bonLivraison.livreurId.toString()) {
            const livreur = await Livreur.findById(livreurId);
            if (!livreur) {
                return res.status(404).json({ message: 'Livreur non trouvé' });
            }
            if (!livreur.estDisponible()) {
                return res.status(400).json({ message: 'Le livreur sélectionné n\'est pas disponible' });
            }
            bonLivraison.livreurId = livreurId;
        }

        // Mettre à jour les champs
        if (dateLivraison) bonLivraison.dateLivraison = dateLivraison;
        if (heureDepart !== undefined) bonLivraison.heureDepart = heureDepart;
        if (heureArrivee !== undefined) bonLivraison.heureArrivee = heureArrivee;
        if (lignes) bonLivraison.lignes = lignes;
        if (statut) bonLivraison.statut = statut;
        if (adresseLivraison) bonLivraison.adresseLivraison = { ...bonLivraison.adresseLivraison, ...adresseLivraison };
        if (signatureClient) bonLivraison.signatureClient = signatureClient;
        if (notes !== undefined) bonLivraison.notes = notes;
        if (observationsLivreur !== undefined) bonLivraison.observationsLivreur = observationsLivreur;
        if (motifEchec !== undefined) bonLivraison.motifEchec = motifEchec;
        if (tracking) bonLivraison.tracking = { ...bonLivraison.tracking, ...tracking };
        if (photosLivraison) bonLivraison.photosLivraison = photosLivraison;

        const bonLivraisonMisAJour = await bonLivraison.save();
        await bonLivraisonMisAJour.populate([
            { path: 'clientId', select: 'nom email telephone adresse' },
            { path: 'livreurId', select: 'nom prenom telephone vehicule' },
            { path: 'vendeurId', select: 'nom prenom' },
            { path: 'responsableId', select: 'nom prenom' }
        ]);

        // Mettre à jour les statistiques du livreur si le statut change
        if (statut && (statut === 'LIVREE' || statut === 'ECHEC')) {
            const livreur = await Livreur.findById(bonLivraison.livreurId);
            if (livreur) {
                await livreur.mettreAJourStatistiques(statut === 'LIVREE');
            }
        }

        res.status(200).json({
            message: 'Bon de livraison mis à jour avec succès',
            bonLivraison: bonLivraisonMisAJour
        });
    } catch (error) {
        console.error('Erreur lors de la mise à jour du bon de livraison:', error);
        if (error.name === 'ValidationError') {
            return res.status(400).json({
                message: 'Données invalides',
                errors: Object.values(error.errors).map(e => e.message)
            });
        }
        res.status(500).json({ message: 'Erreur serveur lors de la mise à jour du bon de livraison' });
    }
});

// DELETE /api/bon-livraisons/:id - Supprimer un bon de livraison
router.delete('/bon-livraisons/:id', canAccessBonLivraison, async (req, res) => {
    try {
        const bonLivraison = await BonLivraison.findById(req.params.id);

        if (!bonLivraison) {
            return res.status(404).json({ message: 'Bon de livraison non trouvé' });
        }

        // Vérifier les permissions
        if (req.userRole === 'RESPONSABLE' && bonLivraison.responsableId.toString() !== req.userId) {
            return res.status(403).json({ message: 'Accès non autorisé à ce bon de livraison' });
        }
        if (req.userRole === 'VENDEUR' && bonLivraison.vendeurId && bonLivraison.vendeurId.toString() !== req.userId) {
            // Vérifier si le vendeur appartient au même responsable
            const vendeur = await User.findById(req.userId).populate('responsables');
            const responsableIds = vendeur.responsables.map(r => r._id.toString());
            if (!responsableIds.includes(bonLivraison.responsableId.toString())) {
                return res.status(403).json({ message: 'Accès non autorisé à ce bon de livraison' });
            }
        }

        // Ne permettre la suppression que si le statut est EN_PREPARATION ou ANNULEE
        if (!['EN_PREPARATION', 'ANNULEE'].includes(bonLivraison.statut)) {
            return res.status(400).json({
                message: 'Impossible de supprimer un bon de livraison en cours ou terminé'
            });
        }

        await BonLivraison.findByIdAndDelete(req.params.id);

        res.status(200).json({ message: 'Bon de livraison supprimé avec succès' });
    } catch (error) {
        console.error('Erreur lors de la suppression du bon de livraison:', error);
        res.status(500).json({ message: 'Erreur serveur lors de la suppression du bon de livraison' });
    }
});

// GET /api/bon-livraisons/statistiques - Récupérer les statistiques des livraisons
router.get('/bon-livraisons/statistiques', canAccessBonLivraison, async (req, res) => {
    try {
        let query = {};

        // Filtrer par responsable si nécessaire
        if (req.userRole === 'RESPONSABLE') {
            query.responsableId = req.userId;
        } else if (req.userRole === 'VENDEUR') {
            const vendeur = await User.findById(req.userId).populate('responsables');
            if (vendeur && vendeur.responsables && vendeur.responsables.length > 0) {
                const responsableIds = vendeur.responsables.map(r => r._id);
                query.$or = [
                    { vendeurId: req.userId },
                    { responsableId: { $in: responsableIds } }
                ];
            } else {
                query.vendeurId = req.userId;
            }
        }

        const { dateDebut, dateFin } = req.query;
        if (dateDebut || dateFin) {
            query.dateLivraison = {};
            if (dateDebut) query.dateLivraison.$gte = new Date(dateDebut);
            if (dateFin) query.dateLivraison.$lte = new Date(dateFin);
        }

        const statistiques = await BonLivraison.aggregate([
            { $match: query },
            {
                $group: {
                    _id: null,
                    totalLivraisons: { $sum: 1 },
                    livraisonsReussies: {
                        $sum: { $cond: [{ $eq: ['$statut', 'LIVREE'] }, 1, 0] }
                    },
                    livraisonsEchouees: {
                        $sum: { $cond: [{ $eq: ['$statut', 'ECHEC'] }, 1, 0] }
                    },
                    livraisonsEnCours: {
                        $sum: { $cond: [{ $eq: ['$statut', 'EN_COURS'] }, 1, 0] }
                    },
                    montantTotalLivre: { $sum: '$montantTotal' },
                    montantMoyen: { $avg: '$montantTotal' }
                }
            }
        ]);

        const stats = statistiques[0] || {
            totalLivraisons: 0,
            livraisonsReussies: 0,
            livraisonsEchouees: 0,
            livraisonsEnCours: 0,
            montantTotalLivre: 0,
            montantMoyen: 0
        };

        // Calculer le taux de réussite
        stats.tauxReussite = stats.totalLivraisons > 0
            ? Math.round((stats.livraisonsReussies / stats.totalLivraisons) * 100)
            : 0;

        res.status(200).json(stats);
    } catch (error) {
        console.error('Erreur lors de la récupération des statistiques:', error);
        res.status(500).json({ message: 'Erreur serveur lors de la récupération des statistiques' });
    }
});

// PUT /api/bon-livraisons/:id/statut - Mettre à jour uniquement le statut
router.put('/bon-livraisons/:id/statut', canAccessBonLivraison, async (req, res) => {
    try {
        const { statut, motifEchec, observationsLivreur } = req.body;

        if (!statut) {
            return res.status(400).json({ message: 'Le statut est obligatoire' });
        }

        const bonLivraison = await BonLivraison.findById(req.params.id);
        if (!bonLivraison) {
            return res.status(404).json({ message: 'Bon de livraison non trouvé' });
        }

        bonLivraison.statut = statut;
        if (motifEchec) bonLivraison.motifEchec = motifEchec;
        if (observationsLivreur) bonLivraison.observationsLivreur = observationsLivreur;

        await bonLivraison.save();

        // Mettre à jour les statistiques du livreur
        if (statut === 'LIVREE' || statut === 'ECHEC') {
            const livreur = await Livreur.findById(bonLivraison.livreurId);
            if (livreur) {
                await livreur.mettreAJourStatistiques(statut === 'LIVREE');
            }
        }

        res.status(200).json({
            message: 'Statut mis à jour avec succès',
            bonLivraison
        });
    } catch (error) {
        console.error('Erreur lors de la mise à jour du statut:', error);
        res.status(500).json({ message: 'Erreur serveur lors de la mise à jour du statut' });
    }
});

// Generate PDF for a bon de livraison
const generateBonLivraisonPDF = async (bonLivraison) => {
  return new Promise(async (resolve, reject) => {
    try {
      // Get template settings and parameters
      const ResponsableTemplate = require('../models/ResponsableTemplateModel');
      const BaseTemplate = require('../models/BaseTemplateModel');
      const Parametres = require('../models/ParametresModel');

      let responsableTemplate = null;
      let baseTemplate = null;
      let params = await Parametres.findOne();

      // Try to get responsable template settings based on the bon de livraison context
      let entrepriseId = null;

      // First, try to get enterprise ID from responsableId
      if (bonLivraison.responsableId) {
        entrepriseId = bonLivraison.responsableId;
        console.log('PDF: Using responsableId as entrepriseId:', entrepriseId);
      }

      if (entrepriseId) {
        responsableTemplate = await ResponsableTemplate.findOne({ responsableId: entrepriseId });
        console.log('PDF: Found responsable template:', responsableTemplate);
      }

      // Get base template
      if (responsableTemplate && responsableTemplate.baseTemplateId) {
        baseTemplate = await BaseTemplate.findById(responsableTemplate.baseTemplateId);
        console.log('PDF: Found base template:', baseTemplate);
      } else {
        // Use default template
        baseTemplate = await BaseTemplate.findOne({ nom: 'Standard' });
        console.log('PDF: Using default template:', baseTemplate);
      }

      // Create PDF document
      const doc = new PDFDocument({ margin: 50 });
      const chunks = [];

      doc.on('data', chunk => chunks.push(chunk));
      doc.on('end', () => resolve(Buffer.concat(chunks)));

      // Colors and styling
      const primaryColor = responsableTemplate?.couleurPrimaire || '#1976d2';
      const secondaryColor = '#f5f5f5';

      // Company information
      const companyName = params?.nomEntreprise || 'Votre Entreprise';
      const companyAddress = params?.adresse || '';
      const companyPhone = params?.telephone || '';
      const companyEmail = params?.email || '';

      // Header
      doc.fontSize(24).fillColor(primaryColor).text('BON DE LIVRAISON', 50, 50);
      doc.fontSize(12).fillColor('black').text(`Numéro: ${bonLivraison.numero}`, 400, 55);

      // Company info (left side)
      doc.fontSize(14).fillColor(primaryColor).text('ÉMETTEUR', 50, 120);
      doc.fontSize(10).fillColor('black');
      doc.text(companyName, 50, 140);
      if (companyAddress) doc.text(companyAddress, 50, 155);
      if (companyPhone) doc.text(`Tél: ${companyPhone}`, 50, 170);
      if (companyEmail) doc.text(`Email: ${companyEmail}`, 50, 185);

      // Client info (right side)
      doc.fontSize(14).fillColor(primaryColor).text('DESTINATAIRE', 350, 120);
      doc.fontSize(10).fillColor('black');
      if (bonLivraison.clientId) {
        doc.text(bonLivraison.clientId.nom || 'N/A', 350, 140);
        if (bonLivraison.clientId.email) doc.text(bonLivraison.clientId.email, 350, 155);
        if (bonLivraison.clientId.telephone) doc.text(`Tél: ${bonLivraison.clientId.telephone}`, 350, 170);
        if (bonLivraison.clientId.adresse) doc.text(bonLivraison.clientId.adresse, 350, 185);
      }

      // Delivery information
      doc.fontSize(14).fillColor(primaryColor).text('INFORMATIONS DE LIVRAISON', 50, 220);
      doc.fontSize(10).fillColor('black');

      let yPos = 240;
      if (bonLivraison.dateLivraison) {
        doc.text(`Date de livraison: ${new Date(bonLivraison.dateLivraison).toLocaleDateString()}`, 50, yPos);
        yPos += 15;
      }

      if (bonLivraison.livreurId) {
        doc.text(`Livreur: ${bonLivraison.livreurId.nom || ''} ${bonLivraison.livreurId.prenom || ''}`, 50, yPos);
        yPos += 15;
      }

      doc.text(`Statut: ${bonLivraison.statut}`, 50, yPos);
      yPos += 15;

      if (bonLivraison.adresseLivraison) {
        doc.text(`Adresse de livraison: ${bonLivraison.adresseLivraison.rue || ''} ${bonLivraison.adresseLivraison.ville || ''} ${bonLivraison.adresseLivraison.codePostal || ''}`, 50, yPos);
        yPos += 15;
      }

      // Items table
      yPos += 20;
      doc.fontSize(14).fillColor(primaryColor).text('DÉTAIL', 50, yPos);
      yPos += 25;

      // Table headers
      doc.rect(50, yPos, 495, 25).fillAndStroke(secondaryColor, primaryColor);
      doc.fontSize(10).fillColor('black');
      doc.text('DESCRIPTION', 60, yPos + 8);
      doc.text('QTÉ', 300, yPos + 8);
      doc.text('PRIX UNIT.', 350, yPos + 8);
      doc.text('TOTAL HT', 450, yPos + 8);

      yPos += 25;

      // Table rows
      if (bonLivraison.lignes && bonLivraison.lignes.length > 0) {
        bonLivraison.lignes.forEach((ligne, index) => {
          const bgColor = index % 2 === 0 ? '#ffffff' : '#f9f9f9';
          doc.rect(50, yPos, 495, 20).fillAndStroke(bgColor, '#dddddd');

          doc.fillColor('black');
          doc.text(ligne.description || ligne.nomProduit || '', 60, yPos + 6);
          doc.text((ligne.quantiteLivree || ligne.quantiteCommandee || 0).toString(), 300, yPos + 6);
          doc.text(`${(ligne.prixUnitaire || 0).toFixed(2)} DT`, 350, yPos + 6);
          doc.text(`${(ligne.montantLigne || ((ligne.quantiteLivree || ligne.quantiteCommandee || 0) * (ligne.prixUnitaire || 0))).toFixed(2)} DT`, 450, yPos + 6);

          yPos += 20;
        });
      }

      // Totals
      yPos += 20;
      const totalHT = bonLivraison.montantHT || bonLivraison.montantTotal || 0;
      const totalTTC = bonLivraison.montantTTC || bonLivraison.montantTotal || 0;
      const tva = totalTTC - totalHT;

      doc.fontSize(10).fillColor('black');
      doc.text(`Total HT: ${totalHT.toFixed(2)} DT`, 400, yPos);
      yPos += 15;
      if (tva > 0) {
        doc.text(`TVA (${bonLivraison.tauxTVA || 19}%): ${tva.toFixed(2)} DT`, 400, yPos);
        yPos += 15;
      }
      doc.fontSize(12).fillColor(primaryColor);
      doc.text(`Total TTC: ${totalTTC.toFixed(2)} DT`, 400, yPos);

      // Notes
      if (bonLivraison.notes) {
        yPos += 40;
        doc.fontSize(12).fillColor(primaryColor).text('NOTES', 50, yPos);
        yPos += 20;
        doc.fontSize(10).fillColor('black').text(bonLivraison.notes, 50, yPos, { width: 495 });
      }

      // Footer
      doc.fontSize(8).fillColor('#666666');
      doc.text(`Bon de livraison généré le ${new Date().toLocaleDateString()} à ${new Date().toLocaleTimeString()}`, 50, 750);

      doc.end();
    } catch (error) {
      console.error('Error generating bon de livraison PDF:', error);
      reject(error);
    }
  });
};

module.exports = router;
